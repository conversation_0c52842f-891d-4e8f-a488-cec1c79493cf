#!/usr/bin/env python3
"""
MoE通信缓冲区诊断工具
"""

import os
import sys
import time
import torch
import subprocess
from typing import Dict, List

def check_cuda_environment():
    """检查CUDA环境"""
    print("=== CUDA环境检查 ===")
    
    try:
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"CUDA版本: {torch.version.cuda}")
            print(f"GPU数量: {torch.cuda.device_count()}")
            
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                print(f"GPU {i}: {props.name}")
                print(f"  内存: {props.total_memory / 1024**3:.1f} GB")
                print(f"  计算能力: {props.major}.{props.minor}")
                
                # 检查内存使用
                torch.cuda.set_device(i)
                allocated = torch.cuda.memory_allocated(i) / 1024**3
                cached = torch.cuda.memory_reserved(i) / 1024**3
                print(f"  已分配内存: {allocated:.2f} GB")
                print(f"  缓存内存: {cached:.2f} GB")
        else:
            print("CUDA不可用")
            
    except Exception as e:
        print(f"CUDA检查失败: {e}")

def check_nccl_environment():
    """检查NCCL环境"""
    print("\n=== NCCL环境检查 ===")
    
    nccl_vars = [
        'NCCL_DEBUG', 'NCCL_TIMEOUT', 'NCCL_SOCKET_TIMEOUT',
        'NCCL_IB_DISABLE', 'NCCL_P2P_DISABLE', 'NCCL_NET_GDR_LEVEL'
    ]
    
    for var in nccl_vars:
        value = os.environ.get(var, '未设置')
        print(f"{var}: {value}")
    
    # 尝试导入NCCL相关模块
    try:
        import torch.distributed as dist
        print(f"torch.distributed可用: True")
        print(f"NCCL后端可用: {dist.is_nccl_available()}")
    except Exception as e:
        print(f"torch.distributed检查失败: {e}")

def check_deepgemm_environment():
    """检查DeepGEMM环境"""
    print("\n=== DeepGEMM环境检查 ===")
    
    deepgemm_vars = [
        'VLLM_USE_DEEP_GEMM', 'VLLM_ALL2ALL_BACKEND'
    ]
    
    for var in deepgemm_vars:
        value = os.environ.get(var, '未设置')
        print(f"{var}: {value}")
    
    # 尝试导入DeepGEMM
    try:
        import deep_gemm
        print("DeepGEMM模块可用: True")
    except ImportError:
        print("DeepGEMM模块可用: False")
    except Exception as e:
        print(f"DeepGEMM检查失败: {e}")

def test_simple_communication():
    """测试简单的GPU通信"""
    print("\n=== GPU通信测试 ===")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过通信测试")
        return
    
    try:
        device_count = torch.cuda.device_count()
        print(f"测试 {device_count} 个GPU之间的通信...")
        
        if device_count < 2:
            print("GPU数量少于2个，跳过多GPU通信测试")
            return
        
        # 简单的张量传输测试
        for i in range(min(2, device_count)):
            torch.cuda.set_device(i)
            tensor = torch.randn(1000, 1000, device=f'cuda:{i}')
            print(f"GPU {i}: 创建张量成功")
            
            # 测试GPU间复制
            if i > 0:
                tensor_copy = tensor.to(f'cuda:0')
                print(f"GPU {i} -> GPU 0: 复制成功")
                
    except Exception as e:
        print(f"GPU通信测试失败: {e}")

def check_memory_fragmentation():
    """检查GPU内存碎片"""
    print("\n=== GPU内存碎片检查 ===")
    
    if not torch.cuda.is_available():
        print("CUDA不可用，跳过内存检查")
        return
    
    try:
        for i in range(torch.cuda.device_count()):
            torch.cuda.set_device(i)
            
            # 获取内存信息
            total = torch.cuda.get_device_properties(i).total_memory
            allocated = torch.cuda.memory_allocated(i)
            cached = torch.cuda.memory_reserved(i)
            
            print(f"GPU {i}:")
            print(f"  总内存: {total / 1024**3:.2f} GB")
            print(f"  已分配: {allocated / 1024**3:.2f} GB")
            print(f"  已缓存: {cached / 1024**3:.2f} GB")
            print(f"  可用: {(total - cached) / 1024**3:.2f} GB")
            
            # 检查内存碎片
            try:
                # 尝试分配大块内存
                test_size = min(1024**3, (total - cached) // 2)  # 1GB或可用内存的一半
                test_tensor = torch.empty(test_size // 4, dtype=torch.float32, device=f'cuda:{i}')
                print(f"  大块内存分配: 成功 ({test_size / 1024**3:.2f} GB)")
                del test_tensor
                torch.cuda.empty_cache()
            except Exception as e:
                print(f"  大块内存分配: 失败 - {e}")
                
    except Exception as e:
        print(f"内存检查失败: {e}")

def suggest_fixes():
    """建议修复方案"""
    print("\n=== 建议修复方案 ===")
    
    suggestions = [
        "1. 禁用专家并行: export VLLM_DISABLE_EXPERT_PARALLEL=1",
        "2. 禁用DeepGEMM: export VLLM_USE_DEEP_GEMM=0",
        "3. 使用标准NCCL: export VLLM_ALL2ALL_BACKEND=nccl",
        "4. 增加超时时间: export NCCL_TIMEOUT=7200",
        "5. 禁用IB和P2P: export NCCL_IB_DISABLE=1 NCCL_P2P_DISABLE=1",
        "6. 减少并行度: --tensor-parallel-size 2",
        "7. 减少内存使用: --gpu-memory-utilization 0.7",
        "8. 使用eager模式: --enforce-eager",
        "9. 禁用前端多进程: --disable-frontend-multiprocessing",
        "10. 清理GPU内存: nvidia-smi --gpu-reset"
    ]
    
    for suggestion in suggestions:
        print(suggestion)

def main():
    print("=== MoE通信缓冲区诊断工具 ===")
    print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    check_cuda_environment()
    check_nccl_environment()
    check_deepgemm_environment()
    test_simple_communication()
    check_memory_fragmentation()
    suggest_fixes()
    
    print("\n=== 诊断完成 ===")

if __name__ == "__main__":
    main()
