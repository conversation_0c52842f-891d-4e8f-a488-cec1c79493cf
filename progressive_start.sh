#!/bin/bash

# 渐进式启动脚本 - 逐步增加复杂度
set -e

MODEL_PATH="/models/DeepSeek-R1-0528"

echo "=== 渐进式VLLM启动 ==="
echo "将按照复杂度递增的顺序尝试启动"

# 基础环境变量
export VLLM_USE_V1=0
export VLLM_ENABLE_V1_MULTIPROCESSING=0
export VLLM_LOGGING_LEVEL=INFO
export PYTHONUNBUFFERED=1

# 清理函数
cleanup() {
    echo "清理进程..."
    pkill -f "vllm serve" || true
    sleep 3
}

# 测试函数
test_startup() {
    local test_name="$1"
    local timeout="$2"
    shift 2
    local cmd=("$@")
    
    echo ""
    echo "=== 测试: $test_name ==="
    echo "命令: ${cmd[*]}"
    echo "超时: ${timeout}秒"
    
    cleanup
    
    # 启动进程并获取PID
    "${cmd[@]}" &
    local pid=$!
    
    # 等待启动或超时
    local count=0
    while [ $count -lt $timeout ]; do
        if ! kill -0 $pid 2>/dev/null; then
            echo "进程已退出"
            return 1
        fi
        
        # 检查是否成功启动（端口8000是否开放）
        if netstat -tulpn 2>/dev/null | grep -q ":8000 "; then
            echo "✓ 启动成功！端口8000已开放"
            echo "等待5秒确认稳定性..."
            sleep 5
            
            if kill -0 $pid 2>/dev/null && netstat -tulpn 2>/dev/null | grep -q ":8000 "; then
                echo "✓ 服务稳定运行"
                kill $pid 2>/dev/null || true
                wait $pid 2>/dev/null || true
                return 0
            else
                echo "✗ 服务不稳定"
                kill $pid 2>/dev/null || true
                wait $pid 2>/dev/null || true
                return 1
            fi
        fi
        
        sleep 2
        count=$((count + 2))
        echo "等待中... ($count/$timeout)"
    done
    
    echo "✗ 启动超时"
    kill $pid 2>/dev/null || true
    wait $pid 2>/dev/null || true
    return 1
}

echo "开始渐进式测试..."

# 测试1: 最基础配置
if test_startup "基础配置" 60 \
    vllm serve "$MODEL_PATH" \
    --trust-remote-code \
    --tensor-parallel-size 1 \
    --dtype bfloat16 \
    --max-model-len 2048 \
    --max-num-seqs 4 \
    --gpu-memory-utilization 0.6 \
    --port 8000 \
    --disable-frontend-multiprocessing; then
    echo "✓ 基础配置成功"
else
    echo "✗ 基础配置失败，请检查模型路径和基础环境"
    exit 1
fi

# 测试2: 增加tensor并行
if test_startup "Tensor并行" 90 \
    vllm serve "$MODEL_PATH" \
    --trust-remote-code \
    --tensor-parallel-size 2 \
    --dtype bfloat16 \
    --max-model-len 4096 \
    --max-num-seqs 8 \
    --gpu-memory-utilization 0.7 \
    --port 8000 \
    --disable-frontend-multiprocessing; then
    echo "✓ Tensor并行成功"
else
    echo "✗ Tensor并行失败"
fi

# 测试3: 增加量化
export VLLM_USE_DEEP_GEMM=0  # 先禁用DeepGEMM
if test_startup "FP8量化" 120 \
    vllm serve "$MODEL_PATH" \
    --trust-remote-code \
    --tensor-parallel-size 2 \
    --quantization fp8 \
    --dtype bfloat16 \
    --max-model-len 4096 \
    --max-num-seqs 8 \
    --gpu-memory-utilization 0.7 \
    --port 8000 \
    --disable-frontend-multiprocessing; then
    echo "✓ FP8量化成功"
else
    echo "✗ FP8量化失败"
fi

# 测试4: 增加更多tensor并行
if test_startup "4路Tensor并行" 150 \
    vllm serve "$MODEL_PATH" \
    --trust-remote-code \
    --tensor-parallel-size 4 \
    --quantization fp8 \
    --dtype bfloat16 \
    --max-model-len 6144 \
    --max-num-seqs 16 \
    --gpu-memory-utilization 0.8 \
    --port 8000 \
    --disable-frontend-multiprocessing \
    --enforce-eager; then
    echo "✓ 4路Tensor并行成功"
else
    echo "✗ 4路Tensor并行失败"
fi

# 测试5: 尝试启用DeepGEMM
export VLLM_USE_DEEP_GEMM=1
export VLLM_ALL2ALL_BACKEND="deepep_low_latency"
if test_startup "DeepGEMM" 180 \
    vllm serve "$MODEL_PATH" \
    --trust-remote-code \
    --tensor-parallel-size 4 \
    --quantization fp8 \
    --dtype bfloat16 \
    --max-model-len 6144 \
    --max-num-seqs 16 \
    --gpu-memory-utilization 0.8 \
    --port 8000 \
    --disable-frontend-multiprocessing \
    --enforce-eager; then
    echo "✓ DeepGEMM成功"
else
    echo "✗ DeepGEMM失败，建议禁用"
    export VLLM_USE_DEEP_GEMM=0
fi

# 测试6: 尝试专家并行（如果之前都成功）
if test_startup "专家并行" 240 \
    vllm serve "$MODEL_PATH" \
    --trust-remote-code \
    --tensor-parallel-size 4 \
    --enable-expert-parallel \
    --quantization fp8 \
    --dtype bfloat16 \
    --max-model-len 8192 \
    --max-num-seqs 32 \
    --gpu-memory-utilization 0.85 \
    --port 8000 \
    --disable-frontend-multiprocessing \
    --enforce-eager; then
    echo "✓ 专家并行成功"
else
    echo "✗ 专家并行失败，这是预期的（通信缓冲区问题）"
fi

echo ""
echo "=== 渐进式测试完成 ==="
echo "建议使用最后一个成功的配置启动服务"

cleanup
