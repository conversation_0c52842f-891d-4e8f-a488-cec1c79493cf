<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reduction::DefaultBlockSwizzle Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reduction.html">reduction</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html">DefaultBlockSwizzle</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reduction::DefaultBlockSwizzle Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="reduction_2threadblock__swizzle_8h_source.html">threadblock_swizzle.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a1ad8edda7b73d23fb5592a531f5736cc"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html#a1ad8edda7b73d23fb5592a531f5736cc">DefaultBlockSwizzle</a> ()</td></tr>
<tr class="memdesc:a1ad8edda7b73d23fb5592a531f5736cc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ctor.  <a href="#a1ad8edda7b73d23fb5592a531f5736cc">More...</a><br /></td></tr>
<tr class="separator:a1ad8edda7b73d23fb5592a531f5736cc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae77863b3e53fa349945fafcd45ac39fa"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE dim3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html#ae77863b3e53fa349945fafcd45ac39fa">swizzle</a> ()</td></tr>
<tr class="memdesc:ae77863b3e53fa349945fafcd45ac39fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Swizzle the block index.  <a href="#ae77863b3e53fa349945fafcd45ac39fa">More...</a><br /></td></tr>
<tr class="separator:ae77863b3e53fa349945fafcd45ac39fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a70bdfa46a246b4fd6d000ffefe8778e4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> dim3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html#a70bdfa46a246b4fd6d000ffefe8778e4">get_grid_layout</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt; const &amp;problem_size, <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt; const &amp;OutputTile)</td></tr>
<tr class="separator:a70bdfa46a246b4fd6d000ffefe8778e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4e26c28b5b715340a399e86213ae4d1"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1DefaultBlockSwizzle.html#ac4e26c28b5b715340a399e86213ae4d1">get_threadblock_offset</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt; const &amp;SubTile)</td></tr>
<tr class="separator:ac4e26c28b5b715340a399e86213ae4d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a1ad8edda7b73d23fb5592a531f5736cc"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> cutlass::reduction::DefaultBlockSwizzle::DefaultBlockSwizzle </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a70bdfa46a246b4fd6d000ffefe8778e4"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> dim3 cutlass::reduction::DefaultBlockSwizzle::get_grid_layout </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt; const &amp;&#160;</td>
          <td class="paramname"><em>problem_size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt; const &amp;&#160;</td>
          <td class="paramname"><em>OutputTile</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac4e26c28b5b715340a399e86213ae4d1"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;3&gt; cutlass::reduction::DefaultBlockSwizzle::get_threadblock_offset </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt; const &amp;&#160;</td>
          <td class="paramname"><em>SubTile</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae77863b3e53fa349945fafcd45ac39fa"></a>
<div class="memitem">
<div class="memproto">
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE dim3 cutlass::reduction::DefaultBlockSwizzle::swizzle </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="reduction_2threadblock__swizzle_8h_source.html">reduction/threadblock_swizzle.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
