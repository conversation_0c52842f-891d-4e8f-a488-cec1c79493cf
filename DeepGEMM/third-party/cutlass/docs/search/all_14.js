var searchData=
[
  ['uint1b_5ft',['uint1b_t',['../namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d',1,'cutlass']]],
  ['uint4b_5ft',['uint4b_t',['../namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d',1,'cutlass']]],
  ['underlying',['Underlying',['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0149454d361ea5885cf5166a920b5145df.html#a1d3913359089881a0bb91579dc0c7d65',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::Row<PERSON>ajor, AdvanceRank, ThreadMap_, Alignment &gt;::Underlying()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_011d3637dbd8bc58bcb020b51bf57fbfc0.html#a36e5de138cd6bfc6614f6c96c84d59a8',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::Underlying()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Ele654c8f6161ae5340f040397a4e2e045c.html#a1d13017ed5ce71fb680966dd292df091',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Underlying()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator2dThreadTile_3_01Shape___00_01Eleb60d066756d1c18f05fceee6a27bdb8a.html#aee3ebdc703c396c2343aa34ec3716179',1,'cutlass::transform::threadblock::RegularTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; 4 &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Underlying()']]],
  ['underlyingarguments',['UnderlyingArguments',['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a7615ad046304360243729c29c65e878a',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::UnderlyingArguments()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#ab5f57fac13e42a08d351ac48c2cc9992',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::UnderlyingArguments()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#a171a1b3ddd40fb9f318e51fa28029f4b',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::UnderlyingArguments()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#aaaf935dfdc267b089de7b304be979d4d',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::UnderlyingArguments()']]],
  ['underlyingiterator',['UnderlyingIterator',['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen89c687c583745a73cb485041911a4c4e.html#aa659934823abe6e573e02103339376d6',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen9838736ad62fae54213fbaf722a989ab.html#aaa983c5d2fb6b44f93567db92ab35964',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemenab63a1e105bf37f6371516cb9e2c5a7a.html#a57dc5b28ed442c9509717c128949cc66',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator_3_01Shape___00_01Elemen809793e785fb4211888c6b4e5dcfcb39.html#a3dea1950c50d2e62a736c7f85eabb4ab',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessType_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__da632779aba661c0f4cfaaa78126b771.html#a814170c20c9e01e5b58d505938b8c1bf',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileAccessIterator2dThreadTile_3_01Shape__7327fa15996bcb8502cdfcc192350fe1.html#ae9d4e095f7008e41aa971d9a5f92ab8c',1,'cutlass::transform::threadblock::PredicatedTileAccessIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessType_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0068b3e874b5d93d11f0fa902c7f1d11d9.html#a31062f8f847a6e962e157f29a2ee6221',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___0041ea81994f8af0d4d071fdb9e66b5ff0.html#abf177445aee9593c35a4343dd082b07d',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, AccessSize &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00f6b3a9dfab5e7c72d5233f7e5e6e3b9b.html#a637707ceb0eaf9c93cb56b6df5942df0',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::ColumnMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator_3_01Shape___00_01Element___00d670f969180a8d182dffb356ebcc957e.html#ae529ae9942e1454e524ba767f76c12f0',1,'cutlass::transform::threadblock::PredicatedTileIterator&lt; Shape_, Element_, layout::RowMajorInterleaved&lt; InterleavedK &gt;, AdvanceRank, ThreadMap_, AccessSize &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_0165b39a630d10785a3558406f9adb99b9.html#a68872ddeed009fde4b7b433b901fb4ee',1,'cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Transpose_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1PredicatedTileIterator2dThreadTile_3_01Shape___00_013671177d6219bfeb0e1b4dc4c1b5bf11.html#a5edb8ebf45b0a2cafe34a144007ff22d',1,'cutlass::transform::threadblock::PredicatedTileIterator2dThreadTile&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Transpose_ &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__eb7d20f8b9d69e0ae5e7ef51dc480867.html#a6a96a638229b569db5c52c9be2f64b92',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajor, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__f04332958a49a47d6fb2b25201764630.html#abb3b7ab4a0d90a14a27cc7a2e8bffc3d',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajor, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__2c1476eaf582bfe972793e17babfe985.html#a35113ec61b349163e40d0684762b6d1a',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__6baada077236f1a368c61c5e11b45b72.html#a320f3007be32a9deb4bc875e0e837421',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__a3c11cf1f00ef7a1efb8389ac6e4c6e0.html#aa745b5e691ec86d1ec68aa880d16450f',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileAccessIterator_3_01Shape___00_01Element__0184b7188941788a96624510a4b2f876.html#addfbe1ed22f504cde6d8332fdbbd9569',1,'cutlass::transform::threadblock::RegularTileAccessIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_017982f81d4ef592e19c8427de2ea933a3.html#ab78b7aa5b373c24ad0b992822f000a08',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01c20d35180520077a5a09b1e33543c1a5.html#afbe000b168397b9954b6d104f3de5928',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value, int(128/sizeof(Element_))&gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_010889a732373c350de9b9a9f6c13cd761.html#a121bd5763c7438baa9ec9d5b34b2a830',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01a31b454d9c930525c1e9ca406a514f40.html#ad18d16103625d26cc2211146e685ea41',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Crosswise &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01793f74bfd8f116a827948ab01a37349a.html#a7a277667005702af19a2a1291694d9f5',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f6f6511b5033cad31083644ac69c54d8.html#a9bd837ef8a350447e9ea1aa386cbc284',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01187f8574e1fe9d7d5e8fbf09bd834bf0.html#a61a04ca4b5cbae72b06f907421e32f00',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_0104ad31bd559a88cc418ae1cab7492ed5.html#a4f764e081db5ddd8756d23d19ad2c09c',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandBCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01bd31b3810c1fedf2e7e5959ff92b5d3d.html#aca84a849e62d818ebe2e5ce4341e244d',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::ColumnMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kRow &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()'],['../classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01b3fa5720e807697de61b9f937b269cd0.html#a198deb729bc0e7260d254ef0ced2fabd',1,'cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::RowMajorVoltaTensorOpMultiplicandCrosswise&lt; sizeof_bits&lt; Element_ &gt;::value, Shape_::kColumn &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::UnderlyingIterator()']]],
  ['underlyingoperator',['UnderlyingOperator',['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#aa48a0b3645f2ef103cb1b2d41218d865',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::UnderlyingOperator()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a55141da9e85b0c3556e531a2a6c19126',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::UnderlyingOperator()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#aa55140ff232b12c3a4bf1e5093282354',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::UnderlyingOperator()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#adb3cad6256057addcd5cf96f469fd679',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::UnderlyingOperator()']]],
  ['uniform',['Uniform',['../structcutlass_1_1Distribution.html#a499f4023e0d42356ce71d38cc32bf92aa0fad91cf4fcbc8ab015053bea77090a6',1,'cutlass::Distribution::Uniform()'],['../structcutlass_1_1Distribution.html#afc30b6976acb39e54f061af1bf2870db',1,'cutlass::Distribution::uniform()']]],
  ['unique_5fptr',['unique_ptr',['../classcutlass_1_1platform_1_1unique__ptr.html',1,'cutlass::platform']]],
  ['unique_5fptr',['unique_ptr',['../classcutlass_1_1platform_1_1unique__ptr.html#aa8a370bc7e4c2d99eb85e7fea27b3179',1,'cutlass::platform::unique_ptr::unique_ptr()'],['../classcutlass_1_1platform_1_1unique__ptr.html#a14c8bf5a5deefe4a6602ccd5c5af364c',1,'cutlass::platform::unique_ptr::unique_ptr(pointer p)']]],
  ['unique_5fptr_3c_20element_2c_20cutlass_3a_3adevice_5fmemory_3a_3aallocation_3a_3adeleter_20_3e',['unique_ptr&lt; Element, cutlass::device_memory::allocation::deleter &gt;',['../classcutlass_1_1platform_1_1unique__ptr.html',1,'cutlass::platform']]],
  ['unique_5fptr_3c_20t_2c_20cutlass_3a_3adevice_5fmemory_3a_3aallocation_3a_3adeleter_20_3e',['unique_ptr&lt; T, cutlass::device_memory::allocation::deleter &gt;',['../classcutlass_1_1platform_1_1unique__ptr.html',1,'cutlass::platform']]],
  ['unsigned_5ftype',['unsigned_type',['../structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a7b02bc71285f43b9442fbd23be3323f5',1,'cutlass::TypeTraits&lt; int8_t &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a3ad36f02c21dbf06d3631793b524b5b7',1,'cutlass::TypeTraits&lt; uint8_t &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01int_01_4.html#aa905e1bad1fb7364a697000def9640d9',1,'cutlass::TypeTraits&lt; int &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#afec24cb83aee1027105d93e6785b6c3f',1,'cutlass::TypeTraits&lt; unsigned &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a09e7639fc05bcc2495d4e55d370532f5',1,'cutlass::TypeTraits&lt; int64_t &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a7cdac983133ee3621cb4d24a5c535828',1,'cutlass::TypeTraits&lt; uint64_t &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01half__t_01_4.html#aa895a8a4ba16f312a2815809151a53f8',1,'cutlass::TypeTraits&lt; half_t &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01float_01_4.html#ab64442443c164c51701ba28af735f6e2',1,'cutlass::TypeTraits&lt; float &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01double_01_4.html#ace0532ebb98ae9afc75f32ba0137374f',1,'cutlass::TypeTraits&lt; double &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a9144fe6ff4a1d2747eaf758a4a2c4e4e',1,'cutlass::TypeTraits&lt; complex&lt; half &gt; &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a5e9a9520dd285a28f29371882e8199b9',1,'cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::unsigned_type()'],['../structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a35bedaea951cee4a763a5ddb6a1a2995',1,'cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::unsigned_type()']]],
  ['unsigned_5ftype',['unsigned_type',['../structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1unsigned__type.html',1,'cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;']]],
  ['update',['update',['../classcutlass_1_1gemm_1_1device_1_1Gemm.html#aaaa871717d2fbe254a434160bc5acc65',1,'cutlass::gemm::device::Gemm::update()'],['../classcutlass_1_1gemm_1_1device_1_1Gemm_3_01ElementA___00_01LayoutA___00_01ElementB___00_01Layout4d0960ae6b1d1bf19e6239dbd002249c.html#a2b6c5275c173d73cffe8e6b6b1ccf2c1',1,'cutlass::gemm::device::Gemm&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, SplitKSerial, Operator_, IsBetaZero &gt;::update()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched.html#ab7c3e9a33a1c62513ec6eee3e2598df6',1,'cutlass::gemm::device::GemmBatched::update()'],['../classcutlass_1_1gemm_1_1device_1_1GemmBatched_3_01ElementA___00_01LayoutA___00_01ElementB___00_0c9bb6f4463ab6085e6008b5d5ad6abfd.html#a9f0c7054068175c1891e4820857603c3',1,'cutlass::gemm::device::GemmBatched&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, AlignmentA, AlignmentB, Operator_ &gt;::update()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex.html#a55acf39b8cdf92ab537348c3f0816513',1,'cutlass::gemm::device::GemmComplex::update()'],['../classcutlass_1_1gemm_1_1device_1_1GemmComplex_3_01ElementA___00_01LayoutA___00_01ElementB___00_07c56401b4df75709ae636675d9980a9a.html#a08446a157a60f7f1e23315c1ece09bce',1,'cutlass::gemm::device::GemmComplex&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ThreadblockSwizzle_, Stages, TransformA, TransformB, SplitKSerial &gt;::update()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel.html#a07ad725857d7eb191cbfc135df22b781',1,'cutlass::gemm::device::GemmSplitKParallel::update()'],['../classcutlass_1_1gemm_1_1device_1_1GemmSplitKParallel_3_01ElementA___00_01LayoutA___00_01ElementBbe7c1f7154ad5b5bf9d4d28301e2b457.html#a44facae3996ed3da5fdb4398e469b773',1,'cutlass::gemm::device::GemmSplitKParallel&lt; ElementA_, LayoutA_, ElementB_, LayoutB_, ElementC_, layout::ColumnMajor, ElementAccumulator_, OperatorClass_, ArchTag_, ThreadblockShape_, WarpShape_, InstructionShape_, EpilogueOutputOp_, ConvertScaledOp_, ReductionOp_, ThreadblockSwizzle_, Stages, kAlignmentA, kAlignmentB, Operator_ &gt;::update()']]],
  ['use_5fdp4a',['use_dp4a',['../classcutlass_1_1gemm_1_1warp_1_1MmaSimt.html#a39e22e3c7afea584e8425064fe72410b',1,'cutlass::gemm::warp::MmaSimt']]]
];
