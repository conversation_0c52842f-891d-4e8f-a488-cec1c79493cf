<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: wmma_sm72.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">wmma_sm72.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="wmma__sm72_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &lt;assert.h&gt;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">namespace </span>arch {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="comment">// WMMA template structure defines nvcuda::wmma::fragments and static assert for</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="comment">// wmma native instruction sizes supported for int8_t</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="comment"></span><span class="keyword">template</span> &lt;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">typename</span> Shape_, </div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">typename</span> LayoutA_, </div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">typename</span> LayoutB_,</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="keyword">typename</span> LayoutC_&gt;</div><div class="line"><a name="l00049"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Wmma_3_01Shape___00_01int8__t_00_01LayoutA___00_01int8__t_00_01LayoutB_505c57bb6818a941dc16f00cf35a9ec0.html">   49</a></span>&#160;<span class="keyword">struct </span>Wmma&lt;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  Shape_,                                   </div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  int8_t,                                   </div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  LayoutA_,                                 </div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  int8_t,                                   </div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  LayoutB_,                                 </div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;  int32_t,                                  </div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  LayoutC_,                                 </div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <a class="code" href="namespacecutlass.html">cutlass</a>::arch::OpMultiplyAdd              </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;&gt; {</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#if defined(CUTLASS_ARCH_WMMA_SM72_ENABLED)</span></div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;  <span class="keyword">using</span> Shape = Shape_;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="keyword">using</span> ElementA = int8_t;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">using</span> LayoutA = LayoutA_;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">using</span> ElementB = int8_t;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">using</span> LayoutB = LayoutB_;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="keyword">using</span> ElementC = int32_t;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="keyword">using</span> LayoutC = LayoutC_;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="keyword">using</span> Operator = cutlass::arch::OpMultiplyAdd;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="comment">// check supported wmma shape for the given multiplicand data types</span></div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;    <a class="code" href="structcutlass_1_1platform_1_1is__same.html">platform::is_same</a>&lt;<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape&lt;16, 16, 16&gt;</a>, Shape&gt;::value ||</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;    <a class="code" href="structcutlass_1_1platform_1_1is__same.html">platform::is_same</a>&lt;<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape&lt; 8, 32, 16&gt;</a>, Shape&gt;::value ||</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;    <a class="code" href="structcutlass_1_1platform_1_1is__same.html">platform::is_same</a>&lt;<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape&lt;32,  8, 16&gt;</a>, Shape&gt;::value,</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;    <span class="stringliteral">&quot;Supported list of wmma operator shape for s8 multiplicands are: 16x16x16, 8x328x16, and 32x8x16&quot;</span>);</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  <span class="comment">// Wmma Fragment</span></div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="keyword">using</span> FragmentA = nvcuda::wmma::fragment&lt;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;          nvcuda::wmma::matrix_a,</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;          Shape::kM,</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;          Shape::kN,</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;          Shape::kK,</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaDataType&lt;ElementA&gt;::Type,</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaLayout&lt;LayoutA&gt;::Layout&gt;;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <span class="keyword">using</span> FragmentB = nvcuda::wmma::fragment&lt;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;          nvcuda::wmma::matrix_b,</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;          Shape::kM,</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;          Shape::kN,</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;          Shape::kK,</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaDataType&lt;ElementB&gt;::Type,</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaLayout&lt;LayoutB&gt;::Layout&gt;;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  <span class="keyword">using</span> FragmentC = nvcuda::wmma::fragment&lt;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;          nvcuda::wmma::accumulator,</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;          Shape::kM,</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;          Shape::kN,</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;          Shape::kK,</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaDataType&lt;ElementC&gt;::Type&gt;;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <span class="keywordtype">void</span> operator()(</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    FragmentC &amp;D, </div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    FragmentA <span class="keyword">const</span> &amp;A, </div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    FragmentB <span class="keyword">const</span> &amp;B, </div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    FragmentC <span class="keyword">const</span> &amp;C)<span class="keyword"> const </span>{</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;      nvcuda::wmma::mma_sync(D, A, B, C);</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  }</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="preprocessor">#else</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(<span class="keyword">false</span>, <span class="stringliteral">&quot;wmma.mma.sync interger type multiplicands is avialable only for SM72 and beyond&quot;</span>);</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;};</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="comment">// WMMA template structure defines nvcuda::wmma::fragments and static assert for</span></div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="comment">// wmma native instruction sizes supported for uint8_t</span></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="comment"></span><span class="keyword">template</span> &lt;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="keyword">typename</span> Shape_, </div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="keyword">typename</span> LayoutA_, </div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="keyword">typename</span> LayoutB_,</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="keyword">typename</span> LayoutC_&gt;</div><div class="line"><a name="l00129"></a><span class="lineno"><a class="line" href="structcutlass_1_1arch_1_1Wmma_3_01Shape___00_01uint8__t_00_01LayoutA___00_01uint8__t_00_01Layout219a464a1248ebfc37aa29bcb10cb1b0.html">  129</a></span>&#160;<span class="keyword">struct </span>Wmma&lt;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  Shape_,                                   </div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  uint8_t,                                  </div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  LayoutA_,                                 </div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  uint8_t,                                  </div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;  LayoutB_,                                 </div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  int32_t,                                  </div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  LayoutC_,                                 </div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <a class="code" href="namespacecutlass.html">cutlass</a>::arch::OpMultiplyAdd              </div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;&gt; {</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="preprocessor">#if defined(CUTLASS_ARCH_WMMA_SM72_ENABLED)</span></div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  <span class="keyword">using</span> Shape = Shape_;</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;  <span class="keyword">using</span> ElementA = uint8_t;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  <span class="keyword">using</span> LayoutA = LayoutA_;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  <span class="keyword">using</span> ElementB = uint8_t;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  <span class="keyword">using</span> LayoutB = LayoutB_;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;  <span class="keyword">using</span> ElementC = int32_t;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  <span class="keyword">using</span> LayoutC = LayoutC_;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <span class="keyword">using</span> Operator = cutlass::arch::OpMultiplyAdd;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  <span class="comment">// check supported wmma shape for the given multiplicand data types</span></div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    <a class="code" href="structcutlass_1_1platform_1_1is__same.html">platform::is_same</a>&lt;<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape&lt;16, 16, 16&gt;</a>, Shape&gt;::value ||</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    <a class="code" href="structcutlass_1_1platform_1_1is__same.html">platform::is_same</a>&lt;<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape&lt; 8, 32, 16&gt;</a>, Shape&gt;::value ||</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    <a class="code" href="structcutlass_1_1platform_1_1is__same.html">platform::is_same</a>&lt;<a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape&lt;32,  8, 16&gt;</a>, Shape&gt;::value,</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <span class="stringliteral">&quot;Supported list of wmma operator shape for u8 multiplicands are: 16x16x16, 8x328x16, and 32x8x16&quot;</span>);</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;  <span class="comment">// Wmma Fragment</span></div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  <span class="keyword">using</span> FragmentA = nvcuda::wmma::fragment&lt;</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;          nvcuda::wmma::matrix_a,</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;          Shape::kM,</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;          Shape::kN,</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;          Shape::kK,</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaDataType&lt;ElementA&gt;::Type,</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaLayout&lt;LayoutA&gt;::Layout&gt;;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  <span class="keyword">using</span> FragmentB = nvcuda::wmma::fragment&lt;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;          nvcuda::wmma::matrix_b,</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;          Shape::kM,</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;          Shape::kN,</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;          Shape::kK,</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaDataType&lt;ElementB&gt;::Type,</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaLayout&lt;LayoutB&gt;::Layout&gt;;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;  <span class="keyword">using</span> FragmentC = nvcuda::wmma::fragment&lt;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;          nvcuda::wmma::accumulator,</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;          Shape::kM,</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;          Shape::kN,</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;          Shape::kK,</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;          <span class="keyword">typename</span> CutlassToWmmaDataType&lt;ElementC&gt;::Type&gt;;</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;  </div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  <span class="keywordtype">void</span> operator()(</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    FragmentC &amp;D, </div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    FragmentA <span class="keyword">const</span> &amp;A, </div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;    FragmentB <span class="keyword">const</span> &amp;B, </div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;    FragmentC <span class="keyword">const</span> &amp;C)<span class="keyword"> const </span>{</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;      nvcuda::wmma::mma_sync(D, A, B, C);</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;  }</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  </div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;<span class="preprocessor">#else</span></div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;    <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(<span class="keyword">false</span>, <span class="stringliteral">&quot;wmma.mma.sync interger type multiplicands is avialable only for SM72 and beyond&quot;</span>);</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;};</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;} <span class="comment">// namespace arch</span></div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1platform_1_1is__same_html"><div class="ttname"><a href="structcutlass_1_1platform_1_1is__same.html">cutlass::platform::is_same</a></div><div class="ttdoc">std::is_same (false specialization) </div><div class="ttdef"><b>Definition:</b> platform.h:394</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
