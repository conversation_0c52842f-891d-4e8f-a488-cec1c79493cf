<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_view.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_view.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__view_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#if !defined(__CUDACC_RTC__)</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &lt;cmath&gt;</span></div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__ref_8h.html">cutlass/tensor_ref.h</a>&quot;</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <span class="keyword">typename</span> Element_,</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  <span class="keyword">typename</span> Layout_</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;&gt;</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html">   56</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> : <span class="keyword">public</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt;Element_, Layout_&gt; {</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a3c2ec2c816648b7c95d9b9e4b24311ae">   60</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">Base</a> = <a class="code" href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt;Element_, Layout_&gt;</a>;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">   63</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">Layout</a> = Layout_;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a48c934ddac84fa964fb9b1364ec44164">   66</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#a48c934ddac84fa964fb9b1364ec44164">ConstTensorRef</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">Base::ConstTensorRef</a>;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a0baf266b25b2dd5adbc1fa262b510990">   69</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorView.html#a3c2ec2c816648b7c95d9b9e4b24311ae">Base</a>;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">   72</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">Element</a> = Element_;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a4d8af7fd842866a218722e7686d6bc3c">   75</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#a4d8af7fd842866a218722e7686d6bc3c">Reference</a> = <a class="code" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">Element</a> &amp;;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#ab50def50420ed64afefe864d108f3c58">   78</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1TensorView.html#ab50def50420ed64afefe864d108f3c58">kRank</a> = Layout::kRank;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#acf1126519c4d26c85ddd20dc66a13e14">   81</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#acf1126519c4d26c85ddd20dc66a13e14">Index</a> = <span class="keyword">typename</span> Layout::Index;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a59dc3d93fa2f5132659f768c9d254627">   84</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#a59dc3d93fa2f5132659f768c9d254627">LongIndex</a> = <span class="keyword">typename</span> Layout::LongIndex;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">   87</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> = <span class="keyword">typename</span> Layout::TensorCoord;</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a6b1bff47f56b66a6da2b2ea35afe583f">   90</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html#a6b1bff47f56b66a6da2b2ea35afe583f">Stride</a> = <span class="keyword">typename</span> Layout::Stride;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">ConstTensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a>&lt;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">platform::remove_const&lt;Element&gt;::type</a> <span class="keyword">const</span>,</div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a71def6d54ed28dfe3b17fde3e6461578">   95</a></span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">Layout</a>&gt;;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">NonConstTensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a>&lt;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;    <span class="keyword">typename</span> platform::remove_const&lt;Element&gt;::type,</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a4b54544b99a8fcfe5228d3614c194a56">  100</a></span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">Layout</a>&gt;;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(kRank &gt; 0, <span class="stringliteral">&quot;Cannot define a zero-rank TensorRef&quot;</span>);</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> extent_;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00120"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#ad554523c77f7166cf0a86f44c359bc32">  120</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#ad554523c77f7166cf0a86f44c359bc32">TensorView</a>(<a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a> = <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a>()): extent_(<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>) {</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;  }</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#aad4fd32dda17f8a4fba57cc04ad33004">  126</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#aad4fd32dda17f8a4fba57cc04ad33004">TensorView</a>(</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">Element</a> *ptr,                         </div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">Layout</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">layout</a>,                 </div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>             </div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;  ):</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">Base</a>(ptr, layout), extent_(extent) {</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  </div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  }</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#af9ed97090d3e4df3e32bb177c6745217">  137</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#af9ed97090d3e4df3e32bb177c6745217">TensorView</a>(</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorView.html#a33385840adab818ef25076edab42175d">ref</a>,                 </div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>             </div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;  ):</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">Base</a>(ref), extent_(extent) {</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  </div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  }</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00147"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a0326d4b92019837828d3b737be4f99af">  147</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#a0326d4b92019837828d3b737be4f99af">TensorView</a>(</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html">NonConstTensorView</a> <span class="keyword">const</span> &amp;view        </div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  ):</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">Base</a>(view), extent_(view.extent_) { }</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00154"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#add0de4f0548c957df00cae0ee0b9257c">  154</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1TensorView.html#add0de4f0548c957df00cae0ee0b9257c">reset</a>(<a class="code" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">Element</a>* ptr, <a class="code" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">Layout</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">layout</a>, <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> size) {</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html#a9c2149162016bc19c7735b824d57eb9e">Base::reset</a>(ptr, layout);</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    this-&gt;<a class="code" href="classcutlass_1_1TensorView.html#a3c3417b9b777f103ec86607a7751e668">resize</a>(extent_);</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  }</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00161"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a3c3417b9b777f103ec86607a7751e668">  161</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1TensorView.html#a3c3417b9b777f103ec86607a7751e668">resize</a>(<a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>) {</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    this-&gt;extent_ = <a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  }</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">  167</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span>&amp; <a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> extent_; }</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00171"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a1bc72b40e616cbb86a306b8ef0be1575">  171</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#acf1126519c4d26c85ddd20dc66a13e14">Index</a> <a class="code" href="classcutlass_1_1TensorView.html#a1bc72b40e616cbb86a306b8ef0be1575">extent</a>(<span class="keywordtype">int</span> dim)<span class="keyword"> const </span>{ <span class="keywordflow">return</span> extent_.at(dim); }</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00175"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a6a6a1f99d06abd8fb3f5a8e4e0fea25e">  175</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1TensorView.html#a6a6a1f99d06abd8fb3f5a8e4e0fea25e">contains</a>(<a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span>&amp; coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> dim = 0; dim &lt; <a class="code" href="classcutlass_1_1TensorView.html#ab50def50420ed64afefe864d108f3c58">kRank</a>; ++dim) {</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;      <span class="keywordflow">if</span> (!(coord[dim] &gt;= 0 &amp;&amp; coord[dim] &lt; <a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>(dim))) {</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        <span class="keywordflow">return</span> <span class="keyword">false</span>;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;      }</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    }</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">true</span>;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;  }</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00187"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a33385840adab818ef25076edab42175d">  187</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <a class="code" href="classcutlass_1_1TensorView.html#a33385840adab818ef25076edab42175d">ref</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorView.html#a0baf266b25b2dd5adbc1fa262b510990">TensorRef</a>(this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">data</a>(), this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">layout</a>());</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;  }</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00193"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#aef27ab5348a53539286057a0da8720fc">  193</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#a48c934ddac84fa964fb9b1364ec44164">ConstTensorRef</a> <a class="code" href="classcutlass_1_1TensorView.html#aef27ab5348a53539286057a0da8720fc">const_ref</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorView.html#a48c934ddac84fa964fb9b1364ec44164">ConstTensorRef</a>(this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">data</a>(), this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">layout</a>());</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;  }</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#ab7794e21b87340f8b73aeb4dca2cb80c">  199</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">ConstTensorView</a> <a class="code" href="classcutlass_1_1TensorView.html#ab7794e21b87340f8b73aeb4dca2cb80c">const_view</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorView.html#a71def6d54ed28dfe3b17fde3e6461578">ConstTensorView</a>(<a class="code" href="classcutlass_1_1TensorView.html#aef27ab5348a53539286057a0da8720fc">const_ref</a>(), extent_);</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;  }</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00205"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a554ebbcae400782746b7cf728ad48093">  205</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> <a class="code" href="classcutlass_1_1TensorView.html#a554ebbcae400782746b7cf728ad48093">subview</a>(</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>,                               </div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span>&amp; location = <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a>()       </div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;  )<span class="keyword"> const </span>{</div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorView.html#ad554523c77f7166cf0a86f44c359bc32">TensorView</a>(<a class="code" href="classcutlass_1_1TensorView.html#a33385840adab818ef25076edab42175d">ref</a>(), extent.clamp(extent_ - location)).<a class="code" href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">add_coord_offset</a>(location);</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;  }</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#ad7c287afe582ff3681a8cd6e53d6a4f5">  215</a></span>&#160;  <span class="keywordtype">size_t</span> <a class="code" href="classcutlass_1_1TensorView.html#ad7c287afe582ff3681a8cd6e53d6a4f5">capacity</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">Base::layout</a>().capacity(extent_);</div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;  }</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00221"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a481a22e9efb27945c1bccae2381c03fb">  221</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> <a class="code" href="classcutlass_1_1TensorView.html#a481a22e9efb27945c1bccae2381c03fb">operator+</a>(</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span>&amp; b            </div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  )<span class="keyword"> const </span>{</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> result(*<span class="keyword">this</span>);</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;    result.<a class="code" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">add_pointer_offset</a>(this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">offset</a>(b));</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  }</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00232"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a5ea9e8f707291de961532f083554c2b6">  232</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a>&amp; <a class="code" href="classcutlass_1_1TensorView.html#a5ea9e8f707291de961532f083554c2b6">operator+=</a>(</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span>&amp; b            </div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  ) {</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;    this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">add_pointer_offset</a>(this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">offset</a>(b));</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;  }</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00242"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a1d57ad0c37d33a544a07e71925605a1d">  242</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a> <a class="code" href="classcutlass_1_1TensorView.html#a1d57ad0c37d33a544a07e71925605a1d">operator-</a>(</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span>&amp; b            </div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  )<span class="keyword"> const </span>{</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> result(*<span class="keyword">this</span>);</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;    result.<a class="code" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">add_pointer_offset</a>(-this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">offset</a>(b));</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;  }</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;</div><div class="line"><a name="l00252"></a><span class="lineno">  252</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00253"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorView.html#a1e1533d9b7942be33df1d122667d5ec6">  253</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView</a>&amp; <a class="code" href="classcutlass_1_1TensorView.html#a1e1533d9b7942be33df1d122667d5ec6">operator-=</a>(</div><div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorCoord</a> <span class="keyword">const</span>&amp; b            </div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;  ) {</div><div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;    this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">add_pointer_offset</a>(-this-&gt;<a class="code" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">offset</a>(b));</div><div class="line"><a name="l00258"></a><span class="lineno">  258</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;  }</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;};</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;</div><div class="line"><a name="l00265"></a><span class="lineno">  265</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">Element</a>,</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;  <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">Layout</a></div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;&gt;</div><div class="line"><a name="l00269"></a><span class="lineno"><a class="line" href="namespacecutlass.html#a29c8084a6b077ef2d0acb6a4e80f11c8">  269</a></span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a> <a class="code" href="namespacecutlass.html#a29c8084a6b077ef2d0acb6a4e80f11c8">make_TensorView</a>(</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">Element</a> *ptr, </div><div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">Layout</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">layout</a>,</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;  <span class="keyword">typename</span> Layout::TensorCoord <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>) {</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;Element, Layout&gt;</a>(ptr, <a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">layout</a>, <a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>);</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;}</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;}  <span class="comment">// namespace cutlass</span></div><div class="ttc" id="classcutlass_1_1TensorView_html_a5ea9e8f707291de961532f083554c2b6"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a5ea9e8f707291de961532f083554c2b6">cutlass::TensorView&lt; Element, Layout &gt;::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView &amp; operator+=(TensorCoord const &amp;b)</div><div class="ttdoc">Returns a TensorRef offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:232</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_ad7c287afe582ff3681a8cd6e53d6a4f5"><div class="ttname"><a href="classcutlass_1_1TensorView.html#ad7c287afe582ff3681a8cd6e53d6a4f5">cutlass::TensorView&lt; Element, Layout &gt;::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE size_t capacity() const</div><div class="ttdoc">Returns the number of scalar elements needed to store tensor. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:215</div></div>
<div class="ttc" id="tensor__ref_8h_html"><div class="ttname"><a href="tensor__ref_8h.html">tensor_ref.h</a></div><div class="ttdoc">Defines a structure containing strides, bounds, and a pointer to tensor data. </div></div>
<div class="ttc" id="structcutlass_1_1platform_1_1remove__const_html_ac3662947fa50251daf58240a9c798085"><div class="ttname"><a href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">cutlass::platform::remove_const::type</a></div><div class="ttdeci">T type</div><div class="ttdef"><b>Definition:</b> platform.h:351</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ac7db3ca62ab1dfe0d3ea08bcadbc9352"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">cutlass::TensorRef::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Element * data() const </div><div class="ttdoc">Returns the pointer to referenced data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:254</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a7d3914dd5042c9c40be9e21a7b4e9ece"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">cutlass::TensorView::extent</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord const &amp; extent() const </div><div class="ttdoc">Returns the extent of the view (the size along each logical dimension). </div><div class="ttdef"><b>Definition:</b> tensor_view.h:167</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_ab50def50420ed64afefe864d108f3c58"><div class="ttname"><a href="classcutlass_1_1TensorView.html#ab50def50420ed64afefe864d108f3c58">cutlass::TensorView::kRank</a></div><div class="ttdeci">static int const kRank</div><div class="ttdoc">Logical rank of tensor index space. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:78</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a3c3417b9b777f103ec86607a7751e668"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a3c3417b9b777f103ec86607a7751e668">cutlass::TensorView::resize</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void resize(TensorCoord extent)</div><div class="ttdoc">Changes the size of the view without affecting pointer or layout. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:161</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a481a22e9efb27945c1bccae2381c03fb"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a481a22e9efb27945c1bccae2381c03fb">cutlass::TensorView&lt; Element, Layout &gt;::operator+</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView operator+(TensorCoord const &amp;b) const</div><div class="ttdoc">Returns a TensorView offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:221</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a1e1533d9b7942be33df1d122667d5ec6"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a1e1533d9b7942be33df1d122667d5ec6">cutlass::TensorView&lt; Element, Layout &gt;::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView &amp; operator-=(TensorCoord const &amp;b)</div><div class="ttdoc">Returns a TensorRef offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:253</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a1d57ad0c37d33a544a07e71925605a1d"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a1d57ad0c37d33a544a07e71925605a1d">cutlass::TensorView&lt; Element, Layout &gt;::operator-</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView operator-(TensorCoord const &amp;b) const</div><div class="ttdoc">Returns a TensorRef offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:242</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a0baf266b25b2dd5adbc1fa262b510990"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a0baf266b25b2dd5adbc1fa262b510990">cutlass::TensorView::TensorRef</a></div><div class="ttdeci">Base TensorRef</div><div class="ttdoc">Underlying TensorRef type. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:69</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ad3c5c9466713f62a5191e720827f34da"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">cutlass::TensorRef::ConstTensorRef</a></div><div class="ttdeci">TensorRef&lt; typename platform::remove_const&lt; Element &gt;::type const, Layout &gt; ConstTensorRef</div><div class="ttdoc">TensorRef to constant data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:179</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a4bed879c428963070de8ffbdc5d6e4f9"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">cutlass::TensorRef::add_coord_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; add_coord_offset(TensorCoord const &amp;coord)</div><div class="ttdoc">Adds an offset to each pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:326</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_afe228764eb67b664fb5ca320c092903b"><div class="ttname"><a href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Element</a></div><div class="ttdeci">Element Element</div><div class="ttdoc">Data type of individual access. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:72</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a71def6d54ed28dfe3b17fde3e6461578"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a71def6d54ed28dfe3b17fde3e6461578">cutlass::TensorView::ConstTensorView</a></div><div class="ttdeci">TensorView&lt; typename platform::remove_const&lt; Element &gt;::type const, Layout &gt; ConstTensorView</div><div class="ttdoc">TensorView pointing to constant memory. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:95</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html"><div class="ttname"><a href="classcutlass_1_1TensorView.html">cutlass::TensorView</a></div><div class="ttdef"><b>Definition:</b> tensor_view.h:56</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_add0de4f0548c957df00cae0ee0b9257c"><div class="ttname"><a href="classcutlass_1_1TensorView.html#add0de4f0548c957df00cae0ee0b9257c">cutlass::TensorView&lt; Element, Layout &gt;::reset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void reset(Element *ptr, Layout const &amp;layout, TensorCoord size)</div><div class="ttdoc">Updates the pointer and layout object. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:154</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a893017197cb29d46773feea6cdbb25db"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdoc">Coordinate in logical tensor space. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:87</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a4d8af7fd842866a218722e7686d6bc3c"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a4d8af7fd842866a218722e7686d6bc3c">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Reference</a></div><div class="ttdeci">Element &amp; Reference</div><div class="ttdoc">Reference type to an element. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:75</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a9c2149162016bc19c7735b824d57eb9e"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a9c2149162016bc19c7735b824d57eb9e">cutlass::TensorRef::reset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void reset(Element *ptr=nullptr)</div><div class="ttdoc">Updates only the pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:235</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef</a></div><div class="ttdef"><b>Definition:</b> tensor_ref.h:146</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a33385840adab818ef25076edab42175d"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a33385840adab818ef25076edab42175d">cutlass::TensorView::ref</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef ref() const </div><div class="ttdoc">Returns a TensorRef pointing to the first element of the tensor. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:187</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a6b1bff47f56b66a6da2b2ea35afe583f"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a6b1bff47f56b66a6da2b2ea35afe583f">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Stride</a></div><div class="ttdeci">typename Layout::Stride Stride</div><div class="ttdoc">Coordinate in storage n-D array. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:90</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a4166ac2a0754574ac21d5d57d74f34e5"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">cutlass::TensorRef::offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex offset(TensorCoord const &amp;coord) const </div><div class="ttdoc">Computes the offset of an index from the origin of the tensor. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:301</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a1bc72b40e616cbb86a306b8ef0be1575"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a1bc72b40e616cbb86a306b8ef0be1575">cutlass::TensorView&lt; Element, Layout &gt;::extent</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index extent(int dim) const</div><div class="ttdoc">Returns the extent along a particular logical dimension. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:171</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a554ebbcae400782746b7cf728ad48093"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a554ebbcae400782746b7cf728ad48093">cutlass::TensorView&lt; Element, Layout &gt;::subview</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView subview(TensorCoord extent, TensorCoord const &amp;location=TensorCoord()) const</div><div class="ttdoc">Returns a Tensor_view given location and size quantities. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:205</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_af9ed97090d3e4df3e32bb177c6745217"><div class="ttname"><a href="classcutlass_1_1TensorView.html#af9ed97090d3e4df3e32bb177c6745217">cutlass::TensorView&lt; Element, Layout &gt;::TensorView</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView(TensorRef const &amp;ref, TensorCoord const &amp;extent)</div><div class="ttdoc">Constructs a TensorView object. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:137</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a0326d4b92019837828d3b737be4f99af"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a0326d4b92019837828d3b737be4f99af">cutlass::TensorView&lt; Element, Layout &gt;::TensorView</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView(NonConstTensorView const &amp;view)</div><div class="ttdoc">Converting constructor from TensorRef to non-constant data. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:147</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_acf1126519c4d26c85ddd20dc66a13e14"><div class="ttname"><a href="classcutlass_1_1TensorView.html#acf1126519c4d26c85ddd20dc66a13e14">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdoc">Index type. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:81</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a3c2ec2c816648b7c95d9b9e4b24311ae"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a3c2ec2c816648b7c95d9b9e4b24311ae">cutlass::TensorView::Base</a></div><div class="ttdeci">cutlass::TensorRef&lt; Element_, Layout_ &gt; Base</div><div class="ttdoc">Base tensor reference. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:60</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_ab7794e21b87340f8b73aeb4dca2cb80c"><div class="ttname"><a href="classcutlass_1_1TensorView.html#ab7794e21b87340f8b73aeb4dca2cb80c">cutlass::TensorView&lt; Element, Layout &gt;::const_view</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstTensorView const_view() const</div><div class="ttdoc">Returns a TensorView to const data. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:199</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a48c934ddac84fa964fb9b1364ec44164"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a48c934ddac84fa964fb9b1364ec44164">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::ConstTensorRef</a></div><div class="ttdeci">typename Base::ConstTensorRef ConstTensorRef</div><div class="ttdoc">TensorRef pointing to constant memory. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:66</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a7771fcd932e36c51c15572305f5b5520"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">cutlass::TensorRef::layout</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Layout &amp; layout()</div><div class="ttdoc">Returns the layout object. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:265</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_aad4fd32dda17f8a4fba57cc04ad33004"><div class="ttname"><a href="classcutlass_1_1TensorView.html#aad4fd32dda17f8a4fba57cc04ad33004">cutlass::TensorView&lt; Element, Layout &gt;::TensorView</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView(Element *ptr, Layout const &amp;layout, TensorCoord const &amp;extent)</div><div class="ttdoc">Constructs a TensorView object. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:126</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_ad554523c77f7166cf0a86f44c359bc32"><div class="ttname"><a href="classcutlass_1_1TensorView.html#ad554523c77f7166cf0a86f44c359bc32">cutlass::TensorView&lt; Element, Layout &gt;::TensorView</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView(TensorCoord const &amp;extent=TensorCoord())</div><div class="ttdoc">Constructs a TensorView object. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:120</div></div>
<div class="ttc" id="namespacecutlass_html_a29c8084a6b077ef2d0acb6a4e80f11c8"><div class="ttname"><a href="namespacecutlass.html#a29c8084a6b077ef2d0acb6a4e80f11c8">cutlass::make_TensorView</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorView&lt; Element, Layout &gt; make_TensorView(Element *ptr, Layout const &amp;layout, typename Layout::TensorCoord const &amp;extent)</div><div class="ttdoc">Constructs a TensorRef, deducing types from arguments. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:269</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a6a6a1f99d06abd8fb3f5a8e4e0fea25e"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a6a6a1f99d06abd8fb3f5a8e4e0fea25e">cutlass::TensorView&lt; Element, Layout &gt;::contains</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool contains(TensorCoord const &amp;coord) const</div><div class="ttdoc">Determines whether a location is within a tensor. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:175</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a6bbcd0e512915565cabfeccdb1b6417d"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">cutlass::TensorRef::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; add_pointer_offset(LongIndex offset_)</div><div class="ttdoc">Adds an offset to each pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:319</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_aef27ab5348a53539286057a0da8720fc"><div class="ttname"><a href="classcutlass_1_1TensorView.html#aef27ab5348a53539286057a0da8720fc">cutlass::TensorView&lt; Element, Layout &gt;::const_ref</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstTensorRef const_ref() const</div><div class="ttdoc">Returns a TensorRef pointing to the first element of the tensor. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:193</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a59dc3d93fa2f5132659f768c9d254627"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a59dc3d93fa2f5132659f768c9d254627">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdoc">Long index used for pointer offsets. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:84</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_ae5168523626a13d6e7391a64bca8caf0"><div class="ttname"><a href="classcutlass_1_1TensorView.html#ae5168523626a13d6e7391a64bca8caf0">cutlass::TensorView&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Layout</a></div><div class="ttdeci">Layout Layout</div><div class="ttdoc">Mapping function from logical coordinate to internal n-D array. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:63</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
