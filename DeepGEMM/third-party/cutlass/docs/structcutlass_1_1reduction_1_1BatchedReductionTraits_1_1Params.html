<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reduction::BatchedReductionTraits&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reduction.html">reduction</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">BatchedReductionTraits</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html">Params</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reduction::BatchedReductionTraits&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="batched__reduction__traits_8h_source.html">batched_reduction_traits.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reduction::BatchedReductionTraits&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params__coll__graph.png" border="0" usemap="#cutlass_1_1reduction_1_1BatchedReductionTraits_3_01ScalarA___00_01ScalarC___00_01ScalarD___00_01ScalarAlphaBeta___00_01ScalarAccum___00_01ReductionSize___00_01OutputTile___00_01SubTile___00_01ThreadShape___00_01Index___00_01BlockSwizzle___00_01maxInReg___00_01maxOutReg___00_01Functor___01_4_1_1Params_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reduction_1_1BatchedReductionTraits_3_01ScalarA___00_01ScalarC___00_01ScalarD___00_01ScalarAlphaBeta___00_01ScalarAccum___00_01ReductionSize___00_01OutputTile___00_01SubTile___00_01ThreadShape___00_01Index___00_01BlockSwizzle___00_01maxInReg___00_01maxOutReg___00_01Functor___01_4_1_1Params_coll__map" id="cutlass_1_1reduction_1_1BatchedReductionTraits_3_01ScalarA___00_01ScalarC___00_01ScalarD___00_01ScalarAlphaBeta___00_01ScalarAccum___00_01ReductionSize___00_01OutputTile___00_01SubTile___00_01ThreadShape___00_01Index___00_01BlockSwizzle___00_01maxInReg___00_01maxOutReg___00_01Functor___01_4_1_1Params_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac27f42beb3625c5183b76b26677c0cb0"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac27f42beb3625c5183b76b26677c0cb0">initialize</a> (<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> m_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> n_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a> alpha_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a> beta_, long long int reduction_stride_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">ScalarA</a> const *d_a_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> lda_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">ScalarC</a> const *d_c_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> ldc_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">ScalarD</a> *d_d_, <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> ldd_)</td></tr>
<tr class="memdesc:ac27f42beb3625c5183b76b26677c0cb0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Initialize the parameters for 2D output tensor.  <a href="#ac27f42beb3625c5183b76b26677c0cb0">More...</a><br /></td></tr>
<tr class="separator:ac27f42beb3625c5183b76b26677c0cb0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:adbd0cf20c4f7033016d1b8fdaca6aeae"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; 3 &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#adbd0cf20c4f7033016d1b8fdaca6aeae">problem_size</a></td></tr>
<tr class="memdesc:adbd0cf20c4f7033016d1b8fdaca6aeae"><td class="mdescLeft">&#160;</td><td class="mdescRight">The dimension of output tensor.  <a href="#adbd0cf20c4f7033016d1b8fdaca6aeae">More...</a><br /></td></tr>
<tr class="separator:adbd0cf20c4f7033016d1b8fdaca6aeae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:afada1cbad87636228fb58d8577bb8470"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#afada1cbad87636228fb58d8577bb8470">alpha</a></td></tr>
<tr class="memdesc:afada1cbad87636228fb58d8577bb8470"><td class="mdescLeft">&#160;</td><td class="mdescRight">The alpha.  <a href="#afada1cbad87636228fb58d8577bb8470">More...</a><br /></td></tr>
<tr class="separator:afada1cbad87636228fb58d8577bb8470"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a805f78cae27c3305c988f251207d85f7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a805f78cae27c3305c988f251207d85f7">beta</a></td></tr>
<tr class="memdesc:a805f78cae27c3305c988f251207d85f7"><td class="mdescLeft">&#160;</td><td class="mdescRight">The beta.  <a href="#a805f78cae27c3305c988f251207d85f7">More...</a><br /></td></tr>
<tr class="separator:a805f78cae27c3305c988f251207d85f7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d1463d473d4226b0d19c581b16ed3b2"><td class="memItemLeft" align="right" valign="top">long long int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a5d1463d473d4226b0d19c581b16ed3b2">reduction_stride</a></td></tr>
<tr class="memdesc:a5d1463d473d4226b0d19c581b16ed3b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">stride between two element that will be sumed  <a href="#a5d1463d473d4226b0d19c581b16ed3b2">More...</a><br /></td></tr>
<tr class="separator:a5d1463d473d4226b0d19c581b16ed3b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af1b12ba220602692e84616b420b00f1c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">ScalarA</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#af1b12ba220602692e84616b420b00f1c">d_a</a></td></tr>
<tr class="separator:af1b12ba220602692e84616b420b00f1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab14fa69759f1d600a28df4fabaf59c28"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ab14fa69759f1d600a28df4fabaf59c28">lda</a></td></tr>
<tr class="separator:ab14fa69759f1d600a28df4fabaf59c28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac79830eaf080ea0ffddd2100db6cf3e1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">ScalarC</a> const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ac79830eaf080ea0ffddd2100db6cf3e1">d_c</a></td></tr>
<tr class="separator:ac79830eaf080ea0ffddd2100db6cf3e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6bdb4dfce17da648b55c7aa68ef5b191"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a6bdb4dfce17da648b55c7aa68ef5b191">ldc</a></td></tr>
<tr class="separator:a6bdb4dfce17da648b55c7aa68ef5b191"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abf9744373a72f3819a616b5a5b3bff22"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">ScalarD</a> *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#abf9744373a72f3819a616b5a5b3bff22">d_d</a></td></tr>
<tr class="separator:abf9744373a72f3819a616b5a5b3bff22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad24096385e51009866b5aafd90b42ad2"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#ad24096385e51009866b5aafd90b42ad2">ldd</a></td></tr>
<tr class="separator:ad24096385e51009866b5aafd90b42ad2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11e54597165b66e3054c6b9f43210760"><td class="memItemLeft" align="right" valign="top">Functor::Params&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html#a11e54597165b66e3054c6b9f43210760">functorParams</a></td></tr>
<tr class="memdesc:a11e54597165b66e3054c6b9f43210760"><td class="mdescLeft">&#160;</td><td class="mdescRight">The functor params.  <a href="#a11e54597165b66e3054c6b9f43210760">More...</a><br /></td></tr>
<tr class="separator:a11e54597165b66e3054c6b9f43210760"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ac27f42beb3625c5183b76b26677c0cb0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> int <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::initialize </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td>
          <td class="paramname"><em>m_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td>
          <td class="paramname"><em>n_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a>&#160;</td>
          <td class="paramname"><em>alpha_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a>&#160;</td>
          <td class="paramname"><em>beta_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">long long int&#160;</td>
          <td class="paramname"><em>reduction_stride_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">ScalarA</a> const *&#160;</td>
          <td class="paramname"><em>d_a_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td>
          <td class="paramname"><em>lda_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">ScalarC</a> const *&#160;</td>
          <td class="paramname"><em>d_c_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td>
          <td class="paramname"><em>ldc_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">ScalarD</a> *&#160;</td>
          <td class="paramname"><em>d_d_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a>&#160;</td>
          <td class="paramname"><em>ldd_</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="afada1cbad87636228fb58d8577bb8470"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a> <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::alpha</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a805f78cae27c3305c988f251207d85f7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a> <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::beta</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af1b12ba220602692e84616b420b00f1c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">ScalarA</a> const* <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::d_a</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac79830eaf080ea0ffddd2100db6cf3e1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">ScalarC</a> const* <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::d_c</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abf9744373a72f3819a616b5a5b3bff22"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">ScalarD</a>* <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::d_d</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a11e54597165b66e3054c6b9f43210760"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">Functor::Params <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::functorParams</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab14fa69759f1d600a28df4fabaf59c28"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::lda</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6bdb4dfce17da648b55c7aa68ef5b191"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::ldc</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad24096385e51009866b5aafd90b42ad2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a> <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::ldd</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adbd0cf20c4f7033016d1b8fdaca6aeae"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;3&gt; <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::problem_size</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5d1463d473d4226b0d19c581b16ed3b2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">long long int <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::Params::reduction_stride</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="batched__reduction__traits_8h_source.html">batched_reduction_traits.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
