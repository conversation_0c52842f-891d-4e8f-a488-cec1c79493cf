<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::device::thread::Gemm&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device.html">device</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device_1_1thread.html">thread</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::device::thread::Gemm&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Thread-level blocked general matrix product.  
</p>

<p><code>#include &lt;<a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2device_2thread_2gemm_8h_source.html">gemm.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a135a896f056553be951ca95f37eeda06"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">ElementA</a> = typename TensorRefA::Element</td></tr>
<tr class="separator:a135a896f056553be951ca95f37eeda06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a07abec0dd58a207238133a62436c0944"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">ElementB</a> = typename TensorRefB::Element</td></tr>
<tr class="separator:a07abec0dd58a207238133a62436c0944"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a88d7a060f9dfab1cd1918d450e3392d8"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a88d7a060f9dfab1cd1918d450e3392d8">ElementC</a> = typename TensorRefC::Element</td></tr>
<tr class="separator:a88d7a060f9dfab1cd1918d450e3392d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9c849673822f71c869e5deb21fa4560b"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a9c849673822f71c869e5deb21fa4560b">Gemm</a> (AccumulatorType initial_accum=AccumulatorType(0))</td></tr>
<tr class="memdesc:a9c849673822f71c869e5deb21fa4560b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a9c849673822f71c869e5deb21fa4560b">More...</a><br /></td></tr>
<tr class="separator:a9c849673822f71c869e5deb21fa4560b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6f0aa8fc056eaba23b030efea31c518e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a6f0aa8fc056eaba23b030efea31c518e">multiply_add</a> (<a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, TensorRefA tensor_a, TensorRefB tensor_b, <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> output_coord=<a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>())</td></tr>
<tr class="memdesc:a6f0aa8fc056eaba23b030efea31c518e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Computes a matrix product.  <a href="#a6f0aa8fc056eaba23b030efea31c518e">More...</a><br /></td></tr>
<tr class="separator:a6f0aa8fc056eaba23b030efea31c518e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5c8d7f3f2ddef533973433bf5c83d73"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a> &amp;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#ac5c8d7f3f2ddef533973433bf5c83d73">epilogue</a> (<a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha, ScalarType beta, TensorRefC tensor_c, TensorRefC tensor_d, <a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> output_coord=<a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>())</td></tr>
<tr class="memdesc:ac5c8d7f3f2ddef533973433bf5c83d73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Performs linear scaling of matrix product and updates output tensor.  <a href="#ac5c8d7f3f2ddef533973433bf5c83d73">More...</a><br /></td></tr>
<tr class="separator:ac5c8d7f3f2ddef533973433bf5c83d73"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a2d63fe67429aa6441e6e247563db1a11"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">ElementA</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a2d63fe67429aa6441e6e247563db1a11">A_tile</a> [OutputTile::kColumn]</td></tr>
<tr class="memdesc:a2d63fe67429aa6441e6e247563db1a11"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile for A operand.  <a href="#a2d63fe67429aa6441e6e247563db1a11">More...</a><br /></td></tr>
<tr class="separator:a2d63fe67429aa6441e6e247563db1a11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5329ece817a4d471dfee042a4eb6f7bd"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">ElementB</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a5329ece817a4d471dfee042a4eb6f7bd">B_tile</a> [OutputTile::kRow]</td></tr>
<tr class="memdesc:a5329ece817a4d471dfee042a4eb6f7bd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile for B operand.  <a href="#a5329ece817a4d471dfee042a4eb6f7bd">More...</a><br /></td></tr>
<tr class="separator:a5329ece817a4d471dfee042a4eb6f7bd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a304c308d4cf13915cf1ba796c506dda6"><td class="memItemLeft" align="right" valign="top">AccumulatorType&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a304c308d4cf13915cf1ba796c506dda6">accum</a> [OutputTile::kColumn][OutputTile::kRow]</td></tr>
<tr class="memdesc:a304c308d4cf13915cf1ba796c506dda6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile for Accumulator.  <a href="#a304c308d4cf13915cf1ba796c506dda6">More...</a><br /></td></tr>
<tr class="separator:a304c308d4cf13915cf1ba796c506dda6"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a135a896f056553be951ca95f37eeda06"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">ElementA</a> =  typename TensorRefA::Element</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a07abec0dd58a207238133a62436c0944"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">ElementB</a> =  typename TensorRefB::Element</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a88d7a060f9dfab1cd1918d450e3392d8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a88d7a060f9dfab1cd1918d450e3392d8">ElementC</a> =  typename TensorRefC::Element</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a9c849673822f71c869e5deb21fa4560b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a> </td>
          <td>(</td>
          <td class="paramtype">AccumulatorType&#160;</td>
          <td class="paramname"><em>initial_accum</em> = <code>AccumulatorType(0)</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ac5c8d7f3f2ddef533973433bf5c83d73"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a>&amp; <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::epilogue </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a>&#160;</td>
          <td class="paramname"><em>problem_size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ScalarType&#160;</td>
          <td class="paramname"><em>alpha</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ScalarType&#160;</td>
          <td class="paramname"><em>beta</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">TensorRefC&#160;</td>
          <td class="paramname"><em>tensor_c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">TensorRefC&#160;</td>
          <td class="paramname"><em>tensor_d</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td>
          <td class="paramname"><em>output_coord</em> = <code><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>()</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6f0aa8fc056eaba23b030efea31c518e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a>&amp; <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::<a class="el" href="structcutlass_1_1multiply__add.html">multiply_add</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a>&#160;</td>
          <td class="paramname"><em>problem_size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">TensorRefA&#160;</td>
          <td class="paramname"><em>tensor_a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">TensorRefB&#160;</td>
          <td class="paramname"><em>tensor_b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td>
          <td class="paramname"><em>output_coord</em> = <code><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>()</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a2d63fe67429aa6441e6e247563db1a11"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">ElementA</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::A_tile[OutputTile::kColumn]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a304c308d4cf13915cf1ba796c506dda6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">AccumulatorType <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::accum[OutputTile::kColumn][OutputTile::kRow]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5329ece817a4d471dfee042a4eb6f7bd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TensorRefA , typename TensorRefB , typename TensorRefC , typename ScalarType , typename AccumulatorType , typename OutputTile , typename InnerProductOp  = multiply_add&lt;AccumulatorType&gt;, typename ConvertOp  = NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">ElementB</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a>&lt; TensorRefA, TensorRefB, TensorRefC, ScalarType, AccumulatorType, OutputTile, InnerProductOp, ConvertOp &gt;::B_tile[OutputTile::kRow]</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2device_2thread_2gemm_8h_source.html">tools/util/include/cutlass/util/reference/device/thread/gemm.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
