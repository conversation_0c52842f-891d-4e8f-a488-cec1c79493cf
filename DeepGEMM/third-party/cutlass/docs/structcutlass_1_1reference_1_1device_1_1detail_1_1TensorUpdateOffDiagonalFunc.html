<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc&lt; Element, Layout &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device.html">device</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">TensorUpdateOffDiagonalFunc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc&lt; Element, Layout &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Computes a random Gaussian distribution.  
 <a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="device_2tensor__fill_8h_source.html">tensor_fill.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc&lt; Element, Layout &gt;:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_3_01Element_00_01Layout_01_4_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_3_01Element_00_01Layout_01_4_coll__map" id="cutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_3_01Element_00_01Layout_01_4_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_1_1Params.html">Params</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameters structure.  <a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_1_1Params.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a1e7ebaecd98820c60973835f871d3ba4"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a1e7ebaecd98820c60973835f871d3ba4">TensorView</a> = <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a1e7ebaecd98820c60973835f871d3ba4">TensorView</a>&lt; Element, Layout &gt;</td></tr>
<tr class="memdesc:a1e7ebaecd98820c60973835f871d3ba4"><td class="mdescLeft">&#160;</td><td class="mdescRight">View type.  <a href="#a1e7ebaecd98820c60973835f871d3ba4">More...</a><br /></td></tr>
<tr class="separator:a1e7ebaecd98820c60973835f871d3ba4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac5e5823e5201202c9705bd532e98dd1d"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">TensorView::Element</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#ac5e5823e5201202c9705bd532e98dd1d">T</a></td></tr>
<tr class="memdesc:ac5e5823e5201202c9705bd532e98dd1d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Scalar type.  <a href="#ac5e5823e5201202c9705bd532e98dd1d">More...</a><br /></td></tr>
<tr class="separator:ac5e5823e5201202c9705bd532e98dd1d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a033d7324eaa485566c9ebc477d4b7119"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorView::TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a033d7324eaa485566c9ebc477d4b7119">TensorCoord</a></td></tr>
<tr class="memdesc:a033d7324eaa485566c9ebc477d4b7119"><td class="mdescLeft">&#160;</td><td class="mdescRight">Coordinate in tensor's index space.  <a href="#a033d7324eaa485566c9ebc477d4b7119">More...</a><br /></td></tr>
<tr class="separator:a033d7324eaa485566c9ebc477d4b7119"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a39a7934332c29cebfc68947d56834188"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a39a7934332c29cebfc68947d56834188">TensorUpdateOffDiagonalFunc</a> (<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_1_1Params.html">Params</a> const &amp;<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a0ad8679159037d6cd2f665af29e33d37">params</a>)</td></tr>
<tr class="memdesc:a39a7934332c29cebfc68947d56834188"><td class="mdescLeft">&#160;</td><td class="mdescRight">Device-side initialization of RNG.  <a href="#a39a7934332c29cebfc68947d56834188">More...</a><br /></td></tr>
<tr class="separator:a39a7934332c29cebfc68947d56834188"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae1f12f1efd80ced9b4976698515bac41"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#ae1f12f1efd80ced9b4976698515bac41">operator()</a> (<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a033d7324eaa485566c9ebc477d4b7119">TensorCoord</a> const &amp;coord)</td></tr>
<tr class="memdesc:ae1f12f1efd80ced9b4976698515bac41"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute random value and update RNG state.  <a href="#ae1f12f1efd80ced9b4976698515bac41">More...</a><br /></td></tr>
<tr class="separator:ae1f12f1efd80ced9b4976698515bac41"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a0ad8679159037d6cd2f665af29e33d37"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_1_1Params.html">Params</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a0ad8679159037d6cd2f665af29e33d37">params</a></td></tr>
<tr class="memdesc:a0ad8679159037d6cd2f665af29e33d37"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameters object.  <a href="#a0ad8679159037d6cd2f665af29e33d37">More...</a><br /></td></tr>
<tr class="separator:a0ad8679159037d6cd2f665af29e33d37"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename Element, typename Layout&gt;<br />
struct cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc&lt; Element, Layout &gt;</h3>

<p>&lt; Layout function </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ac5e5823e5201202c9705bd532e98dd1d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">TensorView::Element</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#ac5e5823e5201202c9705bd532e98dd1d">T</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a033d7324eaa485566c9ebc477d4b7119"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorView::TensorCoord</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a033d7324eaa485566c9ebc477d4b7119">TensorCoord</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1e7ebaecd98820c60973835f871d3ba4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a1e7ebaecd98820c60973835f871d3ba4">TensorView</a> =  <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a1e7ebaecd98820c60973835f871d3ba4">TensorView</a>&lt;Element, Layout&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a39a7934332c29cebfc68947d56834188"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">TensorUpdateOffDiagonalFunc</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_1_1Params.html">Params</a> const &amp;&#160;</td>
          <td class="paramname"><em>params</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ae1f12f1efd80ced9b4976698515bac41"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE void <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc</a>&lt; Element, Layout &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html#a033d7324eaa485566c9ebc477d4b7119">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a0ad8679159037d6cd2f665af29e33d37"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc_1_1Params.html">Params</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorUpdateOffDiagonalFunc.html">cutlass::reference::device::detail::TensorUpdateOffDiagonalFunc</a>&lt; Element, Layout &gt;::params</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="device_2tensor__fill_8h_source.html">device/tensor_fill.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
