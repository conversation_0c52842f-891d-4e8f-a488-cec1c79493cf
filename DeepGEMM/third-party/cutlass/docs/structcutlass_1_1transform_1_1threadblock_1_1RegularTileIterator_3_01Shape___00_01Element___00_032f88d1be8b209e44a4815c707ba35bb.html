<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform_1_1threadblock.html">threadblock</a></li><li class="navelem"><a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_01f96bbeb63e6d4ce4a2551279de3a9f0e.html">RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_032f88d1be8b209e44a4815c707ba35bb.html">Detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_05192e46ead3e35a0208870cfc60f5da5.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::threadblock::RegularTileIterator&lt; Shape_, Element_, layout::VoltaTensorOpMultiplicandCongruous&lt; sizeof_bits&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Internal details made public to facilitate introspection.  
</p>

<p><code>#include &lt;<a class="el" href="regular__tile__iterator__tensor__op__sm70_8h_source.html">regular_tile_iterator_tensor_op_sm70.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a646a2654342dc0134659cc3eece7f027"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_032f88d1be8b209e44a4815c707ba35bb.html#a646a2654342dc0134659cc3eece7f027">kAccessSizeInBits</a> = 128</td></tr>
<tr class="memdesc:a646a2654342dc0134659cc3eece7f027"><td class="mdescLeft">&#160;</td><td class="mdescRight">This iterator is specialized for an access size that is 128 bits in length.  <a href="#a646a2654342dc0134659cc3eece7f027">More...</a><br /></td></tr>
<tr class="separator:a646a2654342dc0134659cc3eece7f027"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aebfe1bfa9754f0b067617277e710fd2d"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator_3_01Shape___00_01Element___00_032f88d1be8b209e44a4815c707ba35bb.html#aebfe1bfa9754f0b067617277e710fd2d">kPointerCount</a> = (ThreadMap::Iterations::kStrided &gt; 1 ? 2 : 1)</td></tr>
<tr class="memdesc:aebfe1bfa9754f0b067617277e710fd2d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of pointers.  <a href="#aebfe1bfa9754f0b067617277e710fd2d">More...</a><br /></td></tr>
<tr class="separator:aebfe1bfa9754f0b067617277e710fd2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a646a2654342dc0134659cc3eece7f027"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename Element_ , int AdvanceRank, typename ThreadMap_ , int Alignment&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html">cutlass::transform::threadblock::RegularTileIterator</a>&lt; Shape_, Element_, <a class="el" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">layout::VoltaTensorOpMultiplicandCongruous</a>&lt; <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kAccessSizeInBits = 128</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aebfe1bfa9754f0b067617277e710fd2d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename Element_ , int AdvanceRank, typename ThreadMap_ , int Alignment&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="classcutlass_1_1transform_1_1threadblock_1_1RegularTileIterator.html">cutlass::transform::threadblock::RegularTileIterator</a>&lt; Shape_, Element_, <a class="el" href="structcutlass_1_1layout_1_1VoltaTensorOpMultiplicandCongruous.html">layout::VoltaTensorOpMultiplicandCongruous</a>&lt; <a class="el" href="structcutlass_1_1sizeof__bits.html">sizeof_bits</a>&lt; Element_ &gt;::value &gt;, AdvanceRank, ThreadMap_, Alignment &gt;::Detail::kPointerCount = (ThreadMap::Iterations::kStrided &gt; 1 ? 2 : 1)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="regular__tile__iterator__tensor__op__sm70_8h_source.html">regular_tile_iterator_tensor_op_sm70.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
