<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::host::detail::TensorFuncBinaryOp&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host.html">host</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">TensorFuncBinaryOp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::host::detail::TensorFuncBinaryOp&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Helper to apply a binary operator in place.  
</p>

<p><code>#include &lt;<a class="el" href="host_2tensor__elementwise_8h_source.html">tensor_elementwise.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::host::detail::TensorFuncBinaryOp&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_00_01ElementD_00_01LayoutD_00_01BinaryFunc_01_4_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_00_01ElementD_00_01LayoutD_00_01BinaryFunc_01_4_coll__map" id="cutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp_3_01ElementA_00_01LayoutA_00_01ElementB_00_01LayoutB_00_01ElementD_00_01LayoutD_00_01BinaryFunc_01_4_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aaab8004c6c977f725947702b017b3c91"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#aaab8004c6c977f725947702b017b3c91">TensorFuncBinaryOp</a> ()</td></tr>
<tr class="memdesc:aaab8004c6c977f725947702b017b3c91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#aaab8004c6c977f725947702b017b3c91">More...</a><br /></td></tr>
<tr class="separator:aaab8004c6c977f725947702b017b3c91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a11eed55eed239f90e009a304e5c1f5ab"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a11eed55eed239f90e009a304e5c1f5ab">TensorFuncBinaryOp</a> (<a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; ElementD, LayoutD &gt; const &amp;view_d_, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementA, LayoutA &gt; const &amp;ref_a_, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementB, LayoutB &gt; const &amp;ref_b_, BinaryFunc <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a2d2bbd2c0a8b8f0292e6820e5d133411">func</a>=BinaryFunc())</td></tr>
<tr class="memdesc:a11eed55eed239f90e009a304e5c1f5ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#a11eed55eed239f90e009a304e5c1f5ab">More...</a><br /></td></tr>
<tr class="separator:a11eed55eed239f90e009a304e5c1f5ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a22f913a5999084fde6565bbb1c90a73b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a22f913a5999084fde6565bbb1c90a73b">operator()</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; LayoutD::kRank &gt; const &amp;coord) const </td></tr>
<tr class="memdesc:a22f913a5999084fde6565bbb1c90a73b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Equality check.  <a href="#a22f913a5999084fde6565bbb1c90a73b">More...</a><br /></td></tr>
<tr class="separator:a22f913a5999084fde6565bbb1c90a73b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a7e9f8d0a477d5d34a31431d9ecf74f4e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; ElementD, LayoutD &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a7e9f8d0a477d5d34a31431d9ecf74f4e">view_d</a></td></tr>
<tr class="memdesc:a7e9f8d0a477d5d34a31431d9ecf74f4e"><td class="mdescLeft">&#160;</td><td class="mdescRight">View of left-hand-side tensor.  <a href="#a7e9f8d0a477d5d34a31431d9ecf74f4e">More...</a><br /></td></tr>
<tr class="separator:a7e9f8d0a477d5d34a31431d9ecf74f4e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77a01e6a9da3273bde175cf5df5d62b4"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementA, LayoutA &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a77a01e6a9da3273bde175cf5df5d62b4">ref_a</a></td></tr>
<tr class="separator:a77a01e6a9da3273bde175cf5df5d62b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a416b2983d56932fa8970a7b988f3d7e6"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementB, LayoutB &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a416b2983d56932fa8970a7b988f3d7e6">ref_b</a></td></tr>
<tr class="separator:a416b2983d56932fa8970a7b988f3d7e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2d2bbd2c0a8b8f0292e6820e5d133411"><td class="memItemLeft" align="right" valign="top">BinaryFunc&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html#a2d2bbd2c0a8b8f0292e6820e5d133411">func</a></td></tr>
<tr class="separator:a2d2bbd2c0a8b8f0292e6820e5d133411"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="aaab8004c6c977f725947702b017b3c91"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementD , typename LayoutD , typename BinaryFunc &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">cutlass::reference::host::detail::TensorFuncBinaryOp</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">TensorFuncBinaryOp</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a11eed55eed239f90e009a304e5c1f5ab"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementD , typename LayoutD , typename BinaryFunc &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">cutlass::reference::host::detail::TensorFuncBinaryOp</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">TensorFuncBinaryOp</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; ElementD, LayoutD &gt; const &amp;&#160;</td>
          <td class="paramname"><em>view_d_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementA, LayoutA &gt; const &amp;&#160;</td>
          <td class="paramname"><em>ref_a_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementB, LayoutB &gt; const &amp;&#160;</td>
          <td class="paramname"><em>ref_b_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">BinaryFunc&#160;</td>
          <td class="paramname"><em>func</em> = <code>BinaryFunc()</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a22f913a5999084fde6565bbb1c90a73b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementD , typename LayoutD , typename BinaryFunc &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">cutlass::reference::host::detail::TensorFuncBinaryOp</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; LayoutD::kRank &gt; const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a2d2bbd2c0a8b8f0292e6820e5d133411"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementD , typename LayoutD , typename BinaryFunc &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">BinaryFunc <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">cutlass::reference::host::detail::TensorFuncBinaryOp</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;::func</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a77a01e6a9da3273bde175cf5df5d62b4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementD , typename LayoutD , typename BinaryFunc &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt;ElementA, LayoutA&gt; <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">cutlass::reference::host::detail::TensorFuncBinaryOp</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;::ref_a</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a416b2983d56932fa8970a7b988f3d7e6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementD , typename LayoutD , typename BinaryFunc &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt;ElementB, LayoutB&gt; <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">cutlass::reference::host::detail::TensorFuncBinaryOp</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;::ref_b</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a7e9f8d0a477d5d34a31431d9ecf74f4e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementD , typename LayoutD , typename BinaryFunc &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt;ElementD, LayoutD&gt; <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorFuncBinaryOp.html">cutlass::reference::host::detail::TensorFuncBinaryOp</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementD, LayoutD, BinaryFunc &gt;::view_d</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="host_2tensor__elementwise_8h_source.html">host/tensor_elementwise.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
