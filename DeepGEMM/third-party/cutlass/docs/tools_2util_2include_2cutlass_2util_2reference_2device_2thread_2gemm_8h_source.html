<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: gemm.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li><li class="navelem"><a class="el" href="dir_01de8928c960cafb028e5f164701e1de.html">reference</a></li><li class="navelem"><a class="el" href="dir_ebbbb6f6f10686db77ac27d0af6d8201.html">device</a></li><li class="navelem"><a class="el" href="dir_cab02fdf7c366af2a4bd9c2fdea5880f.html">thread</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tools/util/include/cutlass/util/reference/device/thread/gemm.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tools_2util_2include_2cutlass_2util_2reference_2device_2thread_2gemm_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__traits_8h.html">cutlass/matrix_traits.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__view_8h.html">cutlass/tensor_view.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="keyword">namespace </span>reference {</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span>device {</div><div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1device_1_1thread.html">   39</a></span>&#160;<span class="keyword">namespace </span>thread {</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="comment">// Note, this is a reference implementation. Performance is not expected to approach peak.</span></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keyword">typename</span> TensorRefA,</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">typename</span> TensorRefB,</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keyword">typename</span> TensorRefC,</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;  <span class="keyword">typename</span> ScalarType,</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;  <span class="keyword">typename</span> AccumulatorType,</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;  <span class="keyword">typename</span> OutputTile,</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;  <span class="keyword">typename</span> InnerProductOp = <a class="code" href="structcutlass_1_1multiply__add.html">multiply_add&lt;AccumulatorType&gt;</a>,</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;  <span class="keyword">typename</span> ConvertOp = <a class="code" href="structcutlass_1_1NumericConverter.html">NumericConverter&lt;typename TensorRefC::Element, ScalarType&gt;</a></div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;&gt;</div><div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">   57</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a> {</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">   59</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">ElementA</a> = <span class="keyword">typename</span> TensorRefA::Element;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">   60</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">ElementB</a> = <span class="keyword">typename</span> TensorRefB::Element;</div><div class="line"><a name="l00061"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a88d7a060f9dfab1cd1918d450e3392d8">   61</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a88d7a060f9dfab1cd1918d450e3392d8">ElementC</a> = <span class="keyword">typename</span> TensorRefC::Element;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a2d63fe67429aa6441e6e247563db1a11">   68</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">ElementA</a> <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a2d63fe67429aa6441e6e247563db1a11">A_tile</a>[OutputTile::kColumn];</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a5329ece817a4d471dfee042a4eb6f7bd">   71</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">ElementB</a> <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a5329ece817a4d471dfee042a4eb6f7bd">B_tile</a>[OutputTile::kRow];</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a304c308d4cf13915cf1ba796c506dda6">   74</a></span>&#160;  AccumulatorType <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a304c308d4cf13915cf1ba796c506dda6">accum</a>[OutputTile::kColumn][OutputTile::kRow];</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a9c849673822f71c869e5deb21fa4560b">   82</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a9c849673822f71c869e5deb21fa4560b">Gemm</a>(AccumulatorType initial_accum = AccumulatorType(0)) {</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;    <span class="comment">// Clear fetch registers</span></div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; OutputTile::kColumn; ++i) {</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;      A_tile[i] = <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">ElementA</a>(0);</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;    }</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; OutputTile::kColumn; ++j) {</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;      B_tile[j] = <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">ElementB</a>(0);</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;    }</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    <span class="comment">// Clear accumulators</span></div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; OutputTile::kColumn; ++j) {</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; OutputTile::kRow; ++i) {</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;        accum[j][i] = initial_accum;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;      }</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;    }</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;  }</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a6f0aa8fc056eaba23b030efea31c518e">  105</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a> &amp; <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a6f0aa8fc056eaba23b030efea31c518e">multiply_add</a>(</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;    <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size,</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    TensorRefA tensor_a,</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;    TensorRefB tensor_b,</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> output_coord = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>()) {</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;    InnerProductOp inner_product_op;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;    <span class="comment">// Loop over the GEMM K dimension</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;    <a class="code" href="cutlass_8h.html#adb3bc73d74b4a4bf13099d5696db3352">CUTLASS_PRAGMA_NO_UNROLL</a></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> k = 0; k &lt; problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a18835ec84cbb6250143327e93697c7e9">k</a>(); ++k) {</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;      <span class="comment">// Fetch a slice of the A matrix</span></div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; OutputTile::kColumn; ++i) {</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        <span class="keywordflow">if</span> (output_coord.row() + i &lt; problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">m</a>()) {</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;          A_tile[i] = tensor_a.at(<a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(output_coord.row() + i, k));</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        }</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;      }</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;      <span class="comment">// Fetch a slice of the B matrix</span></div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; OutputTile::kRow; ++j) {</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        <span class="keywordflow">if</span> (output_coord.column() + j &lt; problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">n</a>()) {</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;          B_tile[j] = tensor_b.at(<a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(k, output_coord.column() + j));</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        }</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;      }</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;      <span class="comment">// Compute an accumulated matrix product</span></div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; OutputTile::kRow; ++j) {</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;        <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;        <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; OutputTile::kColumn; ++i) {</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;          accum[j][i] = inner_product_op(A_tile[i], B_tile[j], accum[j][i]);</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        }</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;      }</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    }</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  }</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00148"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#ac5c8d7f3f2ddef533973433bf5c83d73">  148</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">Gemm</a> &amp; <a class="code" href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#ac5c8d7f3f2ddef533973433bf5c83d73">epilogue</a>(</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    <a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size,</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    ScalarType alpha,</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    ScalarType beta,</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    TensorRefC tensor_c,</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    TensorRefC tensor_d,</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> output_coord = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>()) {</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    ConvertOp convert_op;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    </div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <span class="comment">// Update the output tensor</span></div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> j = 0; j &lt; OutputTile::kRow; ++j) {</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; OutputTile::kColumn; ++i) {</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> coord = output_coord + <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>(i, j);</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;        <span class="keywordflow">if</span> (coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() &lt; problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">m</a>() &amp;&amp; coord.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() &lt; problem_size.<a class="code" href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">n</a>()) {</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;          tensor_d.at(coord) = convert_op(</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;            alpha * ScalarType(accum[j][i]) +</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;            beta * ScalarType(tensor_c.at(coord))</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;          );</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;        }</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;      }</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    }</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;  }</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;};</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;} <span class="comment">// namespace thread</span></div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;} <span class="comment">// namespace device</span></div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;} <span class="comment">// namespace reference</span></div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="structcutlass_1_1multiply__add_html"><div class="ttname"><a href="structcutlass_1_1multiply__add.html">cutlass::multiply_add</a></div><div class="ttdoc">Fused multiply-add. </div><div class="ttdef"><b>Definition:</b> functional.h:92</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html">cutlass::reference::device::thread::Gemm</a></div><div class="ttdoc">Thread-level blocked general matrix product. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:57</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a135a896f056553be951ca95f37eeda06"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a135a896f056553be951ca95f37eeda06">cutlass::reference::device::thread::Gemm::ElementA</a></div><div class="ttdeci">typename TensorRefA::Element ElementA</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:59</div></div>
<div class="ttc" id="namespacecutlass_html_a7419519fa453a121dfa5f26bf87318d9"><div class="ttname"><a href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">cutlass::make_Coord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord&lt; 1 &gt; make_Coord(int _0)</div><div class="ttdoc">Helper to make a 2-element coordinate. </div><div class="ttdef"><b>Definition:</b> coord.h:387</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a5329ece817a4d471dfee042a4eb6f7bd"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a5329ece817a4d471dfee042a4eb6f7bd">cutlass::reference::device::thread::Gemm::B_tile</a></div><div class="ttdeci">ElementB B_tile[OutputTile::kRow]</div><div class="ttdoc">Tile for B operand. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:71</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html">cutlass::gemm::GemmCoord</a></div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:94</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a304c308d4cf13915cf1ba796c506dda6"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a304c308d4cf13915cf1ba796c506dda6">cutlass::reference::device::thread::Gemm::accum</a></div><div class="ttdeci">AccumulatorType accum[OutputTile::kColumn][OutputTile::kRow]</div><div class="ttdoc">Tile for Accumulator. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:74</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a07abec0dd58a207238133a62436c0944"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a07abec0dd58a207238133a62436c0944">cutlass::reference::device::thread::Gemm::ElementB</a></div><div class="ttdeci">typename TensorRefB::Element ElementB</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:60</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a1b29d2cb15360ad5499216859ad5436a"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a1b29d2cb15360ad5499216859ad5436a">cutlass::gemm::GemmCoord::n</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; n() const </div><div class="ttdoc">Returns the GEMM N coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:137</div></div>
<div class="ttc" id="tensor__view_8h_html"><div class="ttname"><a href="tensor__view_8h.html">tensor_view.h</a></div><div class="ttdoc">Defines a structure containing strides and a pointer to tensor data. </div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a18835ec84cbb6250143327e93697c7e9"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a18835ec84cbb6250143327e93697c7e9">cutlass::gemm::GemmCoord::k</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; k() const </div><div class="ttdoc">Returns the GEMM K coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:145</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a9c849673822f71c869e5deb21fa4560b"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a9c849673822f71c869e5deb21fa4560b">cutlass::reference::device::thread::Gemm::Gemm</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Gemm(AccumulatorType initial_accum=AccumulatorType(0))</div><div class="ttdoc">Constructor. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:82</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a2d63fe67429aa6441e6e247563db1a11"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a2d63fe67429aa6441e6e247563db1a11">cutlass::reference::device::thread::Gemm::A_tile</a></div><div class="ttdeci">ElementA A_tile[OutputTile::kColumn]</div><div class="ttdoc">Tile for A operand. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:68</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a88d7a060f9dfab1cd1918d450e3392d8"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a88d7a060f9dfab1cd1918d450e3392d8">cutlass::reference::device::thread::Gemm::ElementC</a></div><div class="ttdeci">typename TensorRefC::Element ElementC</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:61</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_a6f0aa8fc056eaba23b030efea31c518e"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#a6f0aa8fc056eaba23b030efea31c518e">cutlass::reference::device::thread::Gemm::multiply_add</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Gemm &amp; multiply_add(gemm::GemmCoord problem_size, TensorRefA tensor_a, TensorRefB tensor_b, MatrixCoord output_coord=MatrixCoord())</div><div class="ttdoc">Computes a matrix product. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:105</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="cutlass_8h_html_adb3bc73d74b4a4bf13099d5696db3352"><div class="ttname"><a href="cutlass_8h.html#adb3bc73d74b4a4bf13099d5696db3352">CUTLASS_PRAGMA_NO_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_NO_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:111</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm_html_ac5c8d7f3f2ddef533973433bf5c83d73"><div class="ttname"><a href="structcutlass_1_1reference_1_1device_1_1thread_1_1Gemm.html#ac5c8d7f3f2ddef533973433bf5c83d73">cutlass::reference::device::thread::Gemm::epilogue</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Gemm &amp; epilogue(gemm::GemmCoord problem_size, ScalarType alpha, ScalarType beta, TensorRefC tensor_c, TensorRefC tensor_d, MatrixCoord output_coord=MatrixCoord())</div><div class="ttdoc">Performs linear scaling of matrix product and updates output tensor. </div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/reference/device/thread/gemm.h:148</div></div>
<div class="ttc" id="structcutlass_1_1NumericConverter_html"><div class="ttname"><a href="structcutlass_1_1NumericConverter.html">cutlass::NumericConverter</a></div><div class="ttdef"><b>Definition:</b> numeric_conversion.h:59</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmCoord_html_a93515a41db6c4b7e9101067f60d41b8c"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmCoord.html#a93515a41db6c4b7e9101067f60d41b8c">cutlass::gemm::GemmCoord::m</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; m() const </div><div class="ttdoc">Returns the GEMM M coordinate. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:129</div></div>
<div class="ttc" id="matrix__traits_8h_html"><div class="ttname"><a href="matrix__traits_8h.html">matrix_traits.h</a></div><div class="ttdoc">Defines properties of matrices used to denote layout and operands to GEMM kernels. </div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
