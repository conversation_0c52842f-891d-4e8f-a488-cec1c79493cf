<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::host::detail::RandomGaussianFunc&lt; complex&lt; Element &gt; &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host.html">host</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html">RandomGaussianFunc&lt; complex&lt; Element &gt; &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::host::detail::RandomGaussianFunc&lt; complex&lt; Element &gt; &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Partial specialization for initializing a complex value.  
</p>

<p><code>#include &lt;<a class="el" href="host_2tensor__fill_8h_source.html">tensor_fill.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a72b71664e9ddc5ce392e1db822cfdada"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#a72b71664e9ddc5ce392e1db822cfdada">RandomGaussianFunc</a> (uint64_t seed_=0, double mean_=0, double stddev_=1, int int_scale_=-1)</td></tr>
<tr class="separator:a72b71664e9ddc5ce392e1db822cfdada"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a04ad19f3f63c0cba77a76a2ce243b727"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#a04ad19f3f63c0cba77a76a2ce243b727">operator()</a> () const </td></tr>
<tr class="memdesc:a04ad19f3f63c0cba77a76a2ce243b727"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute random value and update RNG state.  <a href="#a04ad19f3f63c0cba77a76a2ce243b727">More...</a><br /></td></tr>
<tr class="separator:a04ad19f3f63c0cba77a76a2ce243b727"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a8239acd9e3b11b0b6a3f26f48f18b508"><td class="memItemLeft" align="right" valign="top">uint64_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#a8239acd9e3b11b0b6a3f26f48f18b508">seed</a></td></tr>
<tr class="separator:a8239acd9e3b11b0b6a3f26f48f18b508"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad8c60b0630a2867fd80a0d09a3cf63cd"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#ad8c60b0630a2867fd80a0d09a3cf63cd">mean</a></td></tr>
<tr class="separator:ad8c60b0630a2867fd80a0d09a3cf63cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4c1cec1d0871654b9e3c5cf132099034"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#a4c1cec1d0871654b9e3c5cf132099034">stddev</a></td></tr>
<tr class="separator:a4c1cec1d0871654b9e3c5cf132099034"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6a906b7ae9c17b6ebe2063d652f3ab50"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#a6a906b7ae9c17b6ebe2063d652f3ab50">int_scale</a></td></tr>
<tr class="separator:a6a906b7ae9c17b6ebe2063d652f3ab50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a61f603633246ab86c8f46e6cbe0f257c"><td class="memItemLeft" align="right" valign="top">double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc_3_01complex_3_01Element_01_4_01_4.html#a61f603633246ab86c8f46e6cbe0f257c">pi</a></td></tr>
<tr class="separator:a61f603633246ab86c8f46e6cbe0f257c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a72b71664e9ddc5ce392e1db822cfdada"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::host::detail::RandomGaussianFunc</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt; &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">RandomGaussianFunc</a> </td>
          <td>(</td>
          <td class="paramtype">uint64_t&#160;</td>
          <td class="paramname"><em>seed_</em> = <code>0</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>mean_</em> = <code>0</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>stddev_</em> = <code>1</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>int_scale_</em> = <code>-1</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a04ad19f3f63c0cba77a76a2ce243b727"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1complex.html">complex</a>&lt;Element&gt; <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::host::detail::RandomGaussianFunc</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt; &gt;::operator() </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a6a906b7ae9c17b6ebe2063d652f3ab50"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">int <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::host::detail::RandomGaussianFunc</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt; &gt;::int_scale</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad8c60b0630a2867fd80a0d09a3cf63cd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">double <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::host::detail::RandomGaussianFunc</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt; &gt;::mean</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a61f603633246ab86c8f46e6cbe0f257c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">double <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::host::detail::RandomGaussianFunc</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt; &gt;::pi</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8239acd9e3b11b0b6a3f26f48f18b508"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::host::detail::RandomGaussianFunc</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt; &gt;::seed</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4c1cec1d0871654b9e3c5cf132099034"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">double <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::host::detail::RandomGaussianFunc</a>&lt; <a class="el" href="classcutlass_1_1complex.html">complex</a>&lt; Element &gt; &gt;::stddev</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="host_2tensor__fill_8h_source.html">host/tensor_fill.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
