<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpXorPopc &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device.html">device</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html">Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpXorPopc &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout369ab66cb5af61d94815b1554b7ffdd3.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::device::Gemm&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpXorPopc &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Partial specialization for XOR-popc.
</p>

<p><code>#include &lt;<a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2device_2gemm_8h_source.html">gemm.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a00dfb8cae5e94bed01f5a09bbd515410"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a00dfb8cae5e94bed01f5a09bbd515410">operator()</a> (<a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementA, LayoutA &gt; tensor_a, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementC, LayoutC &gt; tensor_c, AccumulatorType initial_accum=AccumulatorType(0))</td></tr>
<tr class="separator:a00dfb8cae5e94bed01f5a09bbd515410"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a58c28db4ce4a471e23aa8b9093376d72"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1Gemm_3_01ElementA_00_01LayoutA_00_01ElementB_00_01Layout660562b232f408218828ca5915b7e73a.html#a58c28db4ce4a471e23aa8b9093376d72">operator()</a> (<a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a> problem_size, ScalarType alpha, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementA, LayoutA &gt; tensor_a, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementB, LayoutB &gt; tensor_b, ScalarType beta, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementC, LayoutC &gt; tensor_c, <a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementC, LayoutC &gt; tensor_d, AccumulatorType initial_accum=AccumulatorType(0))</td></tr>
<tr class="separator:a58c28db4ce4a471e23aa8b9093376d72"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a00dfb8cae5e94bed01f5a09bbd515410"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementC , typename LayoutC , typename ScalarType , typename AccumulatorType &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structcutlass_1_1reference_1_1device_1_1Gemm.html">cutlass::reference::device::Gemm</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpXorPopc &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a>&#160;</td>
          <td class="paramname"><em>problem_size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ScalarType&#160;</td>
          <td class="paramname"><em>alpha</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementA, LayoutA &gt;&#160;</td>
          <td class="paramname"><em>tensor_a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementB, LayoutB &gt;&#160;</td>
          <td class="paramname"><em>tensor_b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ScalarType&#160;</td>
          <td class="paramname"><em>beta</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementC, LayoutC &gt;&#160;</td>
          <td class="paramname"><em>tensor_c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">AccumulatorType&#160;</td>
          <td class="paramname"><em>initial_accum</em> = <code>AccumulatorType(0)</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a58c28db4ce4a471e23aa8b9093376d72"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementA , typename LayoutA , typename ElementB , typename LayoutB , typename ElementC , typename LayoutC , typename ScalarType , typename AccumulatorType &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structcutlass_1_1reference_1_1device_1_1Gemm.html">cutlass::reference::device::Gemm</a>&lt; ElementA, LayoutA, ElementB, LayoutB, ElementC, LayoutC, ScalarType, AccumulatorType, arch::OpXorPopc &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1gemm_1_1GemmCoord.html">gemm::GemmCoord</a>&#160;</td>
          <td class="paramname"><em>problem_size</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ScalarType&#160;</td>
          <td class="paramname"><em>alpha</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementA, LayoutA &gt;&#160;</td>
          <td class="paramname"><em>tensor_a</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementB, LayoutB &gt;&#160;</td>
          <td class="paramname"><em>tensor_b</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">ScalarType&#160;</td>
          <td class="paramname"><em>beta</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementC, LayoutC &gt;&#160;</td>
          <td class="paramname"><em>tensor_c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt; ElementC, LayoutC &gt;&#160;</td>
          <td class="paramname"><em>tensor_d</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">AccumulatorType&#160;</td>
          <td class="paramname"><em>initial_accum</em> = <code>AccumulatorType(0)</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="tools_2util_2include_2cutlass_2util_2reference_2device_2gemm_8h_source.html">tools/util/include/cutlass/util/reference/device/gemm.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
