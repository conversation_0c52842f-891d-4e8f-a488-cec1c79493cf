<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_ref.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_ref.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__ref_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="platform_8h.html">cutlass/platform/platform.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="subbyte__reference_8h.html">cutlass/subbyte_reference.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">template</span> &lt;<span class="keywordtype">int</span> Rank&gt;</div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html">   45</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1IdentityTensorLayout.html">IdentityTensorLayout</a> {</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00048"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#a813d116ff0e45679a2a7960a7c10fd1b">   48</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#a813d116ff0e45679a2a7960a7c10fd1b">kRank</a> = Rank;</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965">   51</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965">kStrideRank</a> = Rank;</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">   54</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">Index</a> = int32_t;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">   57</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a> = int64_t;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#a2e2af5a4dea388d9de74bf1c98c610ed">   60</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kRank, Index&gt;</a>;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">   63</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Stride</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;kStrideRank, Index&gt;</a>;</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> stride_;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#a2aff6124a25853f227cdd7f3b36733c4">   81</a></span>&#160;  <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#a2aff6124a25853f227cdd7f3b36733c4">IdentityTensorLayout</a>(<a class="code" href="structcutlass_1_1Coord.html">Stride</a> <span class="keyword">const</span> &amp;<a class="code" href="classcutlass_1_1IdentityTensorLayout.html#adcc4153dde7e6dbd35afd30a28eb9596">stride</a> = <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">Stride</a>()): stride_(<a class="code" href="classcutlass_1_1IdentityTensorLayout.html#adcc4153dde7e6dbd35afd30a28eb9596">stride</a>) { }</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#a471e797514da29a5cad6c61fffe9eb5c">   85</a></span>&#160;  <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a> <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#a471e797514da29a5cad6c61fffe9eb5c">operator()</a>(<a class="code" href="structcutlass_1_1Coord.html">Coord&lt;Rank&gt;</a> <span class="keyword">const</span> &amp;coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;    <span class="keywordflow">return</span> coord.<a class="code" href="structcutlass_1_1Coord.html#a057a417a4d4a6e2f69e0b55a6f7ee902">dot</a>(stride_);</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;  }</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#adcc4153dde7e6dbd35afd30a28eb9596">   91</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#adcc4153dde7e6dbd35afd30a28eb9596">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;  }</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#a041b285984b1940d70a2b7768416e996">   97</a></span>&#160;  <a class="code" href="structcutlass_1_1Coord.html">Stride</a> &amp; <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#a041b285984b1940d70a2b7768416e996">stride</a>() {</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;    <span class="keywordflow">return</span> stride_;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  }</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="classcutlass_1_1IdentityTensorLayout.html#a3dc530520b5eb35bc57c75de7954b59f">  103</a></span>&#160;  <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">LongIndex</a> <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#a3dc530520b5eb35bc57c75de7954b59f">capacity</a>(<a class="code" href="structcutlass_1_1Coord.html">TensorCoord</a> <span class="keyword">const</span> &amp;size)<span class="keyword"> const </span>{</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;    <span class="keywordtype">int</span> idx = stride_.<a class="code" href="structcutlass_1_1Coord.html#abe58b7c8f153a6029c2adc173f340fe0">max_dim_index</a>();</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;    <span class="keywordflow">return</span> stride_[idx] * size[idx];</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  }</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;};</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="comment">/* \brief TensorRef is a template for objects pointing to the start of tensors of arbitrary rank</span></div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="comment">          and layout within memory. A TensorRef combines a pointer and a Layout concept</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;<span class="comment">  Examples:</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;<span class="comment">  (These examples use helpers for matrix layouts defined in cutlass/layout/matrix.h)</span></div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="comment">  1. Column-major matrix may be represented as a rank=2 tensor:</span></div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;<span class="comment">    TensorRef&lt;float, layout::ColumnMajor&gt; A(ptr_A, ldm);</span></div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="comment">  2. Row-major matrix may be represented as a rank=2 tensor:</span></div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;<span class="comment">    TensorRef&lt;float, layout::RowMajor&gt; B(ptr_A, ldm);</span></div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;<span class="comment">  3. An interleaved matrix may be represented as a rank=2 tensor:</span></div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;<span class="comment">    TensorRef&lt;int8_t, layout::ColumnMajorInterleaved&lt;32&gt; &gt; C;</span></div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="comment">  4. A helper exists to define a TensorRef for a contiguous matrix whose layout</span></div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="comment">     is not known at compile time.</span></div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;<span class="comment">    int ldm;                     // leading dimension</span></div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="comment">    layout::Matrix kind;         // Could be layout::Matrix::kRowMajor or layout::Matrix::kColumnMajor</span></div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;<span class="comment">    </span></div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="comment">    TensorRef&lt;int, layout::ContiguousMatrix&gt; E(ptr_E, {ldm, kind});</span></div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;<span class="comment">*/</span></div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  <span class="keyword">typename</span> Element_,</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  <span class="keyword">typename</span> Layout_</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;&gt;</div><div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html">  146</a></span>&#160;<span class="keyword">class </span><a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> {</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">  149</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a> = Element_;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">  152</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a> = Layout_;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">Reference</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;    <a class="code" href="structcutlass_1_1sizeof__bits.html">sizeof_bits&lt;Element&gt;::value</a> &gt;= 8,</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a> &amp;,</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;    <a class="code" href="classcutlass_1_1SubbyteReference.html">SubbyteReference&lt;Element&gt;</a></div><div class="line"><a name="l00159"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">  159</a></span>&#160;    &gt;::type;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;</div><div class="line"><a name="l00162"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a87c5c1c23b67b7182a177ef8d9437edd">  162</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRank = Layout::kRank;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div><div class="line"><a name="l00165"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">  165</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">Index</a> = <span class="keyword">typename</span> Layout::Index;</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">  168</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">LongIndex</a> = <span class="keyword">typename</span> Layout::LongIndex;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;</div><div class="line"><a name="l00171"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">  171</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> = <span class="keyword">typename</span> Layout::TensorCoord;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a7fb24829405f63b9fa9bbcb110141d9e">  174</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html#a7fb24829405f63b9fa9bbcb110141d9e">Stride</a> = <span class="keyword">typename</span> Layout::Stride;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">ConstTensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">platform::remove_const&lt;Element&gt;::type</a> <span class="keyword">const</span>,</div><div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ad3c5c9466713f62a5191e720827f34da">  179</a></span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a>&gt;;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">NonConstTensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a>&lt;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;    <span class="keyword">typename</span> platform::remove_const&lt;Element&gt;::type,</div><div class="line"><a name="l00184"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a68c6f7efc142acf71dd3908b00494b38">  184</a></span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a>&gt;;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;  <a class="code" href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a>(kRank &gt; 0, <span class="stringliteral">&quot;Cannot define a zero-rank TensorRef&quot;</span>);</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160; <span class="keyword">private</span>:</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a>* ptr_;</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a> layout_;</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160; <span class="keyword">public</span>:</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00207"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a26c607b811a8f828f5ec5b00f3b874ea">  207</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a26c607b811a8f828f5ec5b00f3b874ea">TensorRef</a>(</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a> *ptr = <span class="keyword">nullptr</span>,                   </div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a> <span class="keyword">const</span> &amp;layout = <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a>()           </div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;  ):</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    ptr_(ptr), layout_(layout) {</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  </div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  }</div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00217"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a2b79f0d7c0c70774199ba4226bae0fe0">  217</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a2b79f0d7c0c70774199ba4226bae0fe0">TensorRef</a>(</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">NonConstTensorRef</a> <span class="keyword">const</span> &amp;ref              </div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;  ):</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;    ptr_(ref.data()), layout_(ref.layout()) { }</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00224"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a5c5c66a59e9759f11f832fb71f4234c2">  224</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">ConstTensorRef</a> <a class="code" href="classcutlass_1_1TensorRef.html#a5c5c66a59e9759f11f832fb71f4234c2">const_ref</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorRef.html">ConstTensorRef</a>(ptr_, layout_);</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;  }</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00229"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a66a9bab939e2d57c130bb76a5527f482">  229</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">NonConstTensorRef</a> <a class="code" href="classcutlass_1_1TensorRef.html#a66a9bab939e2d57c130bb76a5527f482">non_const_ref</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorRef.html">NonConstTensorRef</a>(<span class="keyword">const_cast&lt;</span>typename platform::remove_const&lt;Element&gt;::type *<span class="keyword">&gt;</span>(ptr_), layout_);</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  }</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00235"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a9c2149162016bc19c7735b824d57eb9e">  235</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1TensorRef.html#a9c2149162016bc19c7735b824d57eb9e">reset</a>(<a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a>* ptr = <span class="keyword">nullptr</span>) {</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;    ptr_ = ptr;</div><div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;  }</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00241"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a1c539841446205c482fe18cc0a99d913">  241</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1TensorRef.html#a1c539841446205c482fe18cc0a99d913">reset</a>(<a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a>* ptr, <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a> <span class="keyword">const</span> &amp;layout) {</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;    ptr_ = ptr;</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;    layout_ = layout;</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;  }</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00248"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ac968ea9cb34fa99d29c64608c53bd4d4">  248</a></span>&#160;  <span class="keywordtype">bool</span> <a class="code" href="classcutlass_1_1TensorRef.html#ac968ea9cb34fa99d29c64608c53bd4d4">good</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;    <span class="keywordflow">return</span> ptr_ != <span class="keyword">nullptr</span>;</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;  }</div><div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div><div class="line"><a name="l00253"></a><span class="lineno">  253</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00254"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">  254</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a> * <a class="code" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">data</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> ptr_; }</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00258"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a965e8b3b7f92dc51d4d3821ea6a25012">  258</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">Reference</a> <a class="code" href="classcutlass_1_1TensorRef.html#a965e8b3b7f92dc51d4d3821ea6a25012">data</a>(<a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">LongIndex</a> idx)<span class="keyword"> const </span>{</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1ReferenceFactory.html">ReferenceFactory&lt;typename platform::remove_const&lt;Element&gt;::type</a>,</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;                            (sizeof_bits&lt;Element&gt;::value &lt; 8)&gt;::<span class="keyword">get</span>(ptr_, idx);</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;  }</div><div class="line"><a name="l00262"></a><span class="lineno">  262</span>&#160;</div><div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00265"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">  265</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a> &amp; <a class="code" href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">layout</a>() {</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;    <span class="keywordflow">return</span> layout_;</div><div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;  }</div><div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;</div><div class="line"><a name="l00270"></a><span class="lineno">  270</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00271"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#aca7d373c988016ee946f85ea958b21a7">  271</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a> <a class="code" href="classcutlass_1_1TensorRef.html#aca7d373c988016ee946f85ea958b21a7">layout</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;    <span class="keywordflow">return</span> layout_;</div><div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;  }</div><div class="line"><a name="l00274"></a><span class="lineno">  274</span>&#160;</div><div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00277"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a191e88bc0fb310be655d700e937ab97c">  277</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a7fb24829405f63b9fa9bbcb110141d9e">Stride</a> <a class="code" href="classcutlass_1_1TensorRef.html#a191e88bc0fb310be655d700e937ab97c">stride</a>()<span class="keyword"> const </span>{</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;    <span class="keywordflow">return</span> layout_.stride();</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  }</div><div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;</div><div class="line"><a name="l00282"></a><span class="lineno">  282</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a5fbf003d2e2321f816be60b0cbd0cfb7">  283</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a7fb24829405f63b9fa9bbcb110141d9e">Stride</a> &amp; <a class="code" href="classcutlass_1_1TensorRef.html#a5fbf003d2e2321f816be60b0cbd0cfb7">stride</a>() {</div><div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;    <span class="keywordflow">return</span> layout_.stride();</div><div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;  }</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;</div><div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00289"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a300283c640d4e6aadc9c695befa26fec">  289</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">Index</a> <a class="code" href="classcutlass_1_1TensorRef.html#a300283c640d4e6aadc9c695befa26fec">stride</a>(<span class="keywordtype">int</span> dim)<span class="keyword"> const </span>{</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;    <span class="keywordflow">return</span> layout_.stride().at(dim);</div><div class="line"><a name="l00291"></a><span class="lineno">  291</span>&#160;  }</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00295"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a425adc5418cc80c9929579046d3111ef">  295</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">Index</a> &amp; <a class="code" href="classcutlass_1_1TensorRef.html#a425adc5418cc80c9929579046d3111ef">stride</a>(<span class="keywordtype">int</span> dim) {</div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;    <span class="keywordflow">return</span> layout_.stride().at(dim);</div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  }</div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00301"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">  301</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">LongIndex</a> <a class="code" href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">offset</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span>&amp; coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;    <span class="keywordflow">return</span> layout_(coord);</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;  }</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00307"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">  307</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">Reference</a> <a class="code" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">at</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span>&amp; coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;    <span class="keywordflow">return</span> data(offset(coord));</div><div class="line"><a name="l00309"></a><span class="lineno">  309</span>&#160;  }</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00313"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a10e36681cae687d9a45ec01bad2626b7">  313</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">Reference</a> <a class="code" href="classcutlass_1_1TensorRef.html#a10e36681cae687d9a45ec01bad2626b7">operator[]</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span>&amp; coord)<span class="keyword"> const </span>{</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    <span class="keywordflow">return</span> data(offset(coord));</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;  }</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00319"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">  319</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> &amp; <a class="code" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">LongIndex</a> offset_) {</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;    ptr_ += offset_;</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;  }</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00326"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">  326</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> &amp; <a class="code" href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">add_coord_offset</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span> &amp;coord) {</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;    add_pointer_offset(offset(coord));</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;  }</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00333"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ad4e1a9d4912a18547cd5391d63e8e7ac">  333</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <a class="code" href="classcutlass_1_1TensorRef.html#ad4e1a9d4912a18547cd5391d63e8e7ac">operator+</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span>&amp; b)<span class="keyword"> const </span>{</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> result(*<span class="keyword">this</span>);</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    result.<a class="code" href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">add_coord_offset</a>(b);</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;  }</div><div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;</div><div class="line"><a name="l00340"></a><span class="lineno">  340</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00341"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ae82b8ba3bbf7ebb28fe063932db3dc6b">  341</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> &amp; <a class="code" href="classcutlass_1_1TensorRef.html#ae82b8ba3bbf7ebb28fe063932db3dc6b">operator+=</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span>&amp; b) {</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;    add_coord_offset(b);</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;  }</div><div class="line"><a name="l00345"></a><span class="lineno">  345</span>&#160;</div><div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00348"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#aa275fd660406665884f25c6f693a8892">  348</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <a class="code" href="classcutlass_1_1TensorRef.html#aa275fd660406665884f25c6f693a8892">operator-</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span>&amp; b)<span class="keyword"> const </span>{</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> result(*<span class="keyword">this</span>);</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;    result.<a class="code" href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">add_pointer_offset</a>(-offset(b));</div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;    <span class="keywordflow">return</span> result;</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;  }</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00356"></a><span class="lineno"><a class="line" href="classcutlass_1_1TensorRef.html#ac5f24cda8149ddfa8f19467ff2629f49">  356</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> &amp; <a class="code" href="classcutlass_1_1TensorRef.html#ac5f24cda8149ddfa8f19467ff2629f49">operator-=</a>(<a class="code" href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">TensorCoord</a> <span class="keyword">const</span>&amp; b) {</div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;    add_pointer_offset(-offset(b));</div><div class="line"><a name="l00358"></a><span class="lineno">  358</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;  }</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;};</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;</div><div class="line"><a name="l00363"></a><span class="lineno">  363</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;  <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">Element</a>,</div><div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;  <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">Layout</a></div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;&gt;</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;<a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00368"></a><span class="lineno"><a class="line" href="namespacecutlass.html#accfe64331a1403e14cc312d1b4c844e1">  368</a></span>&#160;<a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a> <a class="code" href="namespacecutlass.html#accfe64331a1403e14cc312d1b4c844e1">make_TensorRef</a>(Element *ptr, Layout <span class="keyword">const</span> &amp;layout) {</div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;  <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>(ptr, layout);</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;}</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;<span class="comment">// Partial specializations to handle degenerate and sub-byte cases.</span></div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;  <span class="keyword">typename</span> Element,</div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;  <span class="keyword">typename</span> Layout</div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;&gt;</div><div class="line"><a name="l00382"></a><span class="lineno"><a class="line" href="namespacecutlass.html#aa43b0a7d59635cb2d9ac96a077c988c3">  382</a></span>&#160;<span class="keywordtype">bool</span> <a class="code" href="namespacecutlass.html#aa43b0a7d59635cb2d9ac96a077c988c3">TensorRef_aligned</a>(<a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a> <span class="keyword">const</span> &amp;ref, <span class="keywordtype">int</span> alignment) {</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;</div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;  <span class="keywordtype">int</span> <span class="keyword">const</span> kStrideRank = Layout::kStrideRank;</div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;</div><div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;  <span class="keywordflow">if</span> (reinterpret_cast&lt;uintptr_t&gt;(ref.<a class="code" href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">data</a>()) % alignment) {</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;    <span class="keywordflow">return</span> <span class="keyword">false</span>;</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;  }</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;  <span class="keywordflow">for</span> (<span class="keywordtype">int</span> i = 0; i &lt; <a class="code" href="classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965">kStrideRank</a>; ++i) {</div><div class="line"><a name="l00392"></a><span class="lineno">  392</span>&#160;    <span class="keywordflow">if</span> (ref.<a class="code" href="classcutlass_1_1TensorRef.html#a191e88bc0fb310be655d700e937ab97c">stride</a>(i) % alignment) {</div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;      <span class="keywordflow">return</span> <span class="keyword">false</span>;</div><div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;    }</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;  }</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;  <span class="keywordflow">return</span> <span class="keyword">true</span>;</div><div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;}</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_adcc4153dde7e6dbd35afd30a28eb9596"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#adcc4153dde7e6dbd35afd30a28eb9596">cutlass::IdentityTensorLayout::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const </div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:91</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_a471e797514da29a5cad6c61fffe9eb5c"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#a471e797514da29a5cad6c61fffe9eb5c">cutlass::IdentityTensorLayout::operator()</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex operator()(Coord&lt; Rank &gt; const &amp;coord) const </div><div class="ttdoc">Returns the offset of a coordinate in linear memory. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:85</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a300283c640d4e6aadc9c695befa26fec"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a300283c640d4e6aadc9c695befa26fec">cutlass::TensorRef&lt; Element, Layout &gt;::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index stride(int dim) const</div><div class="ttdoc">Returns the layout object&amp;#39;s stride in a given physical dimension. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:289</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a7fb24829405f63b9fa9bbcb110141d9e"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a7fb24829405f63b9fa9bbcb110141d9e">cutlass::TensorRef&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Stride</a></div><div class="ttdeci">typename Layout::Stride Stride</div><div class="ttdoc">Layout&amp;#39;s stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:174</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a425adc5418cc80c9929579046d3111ef"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a425adc5418cc80c9929579046d3111ef">cutlass::TensorRef&lt; Element, Layout &gt;::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index &amp; stride(int dim)</div><div class="ttdoc">Returns the layout object&amp;#39;s stride in a given physical dimension. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:295</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a5fbf003d2e2321f816be60b0cbd0cfb7"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a5fbf003d2e2321f816be60b0cbd0cfb7">cutlass::TensorRef&lt; Element, Layout &gt;::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the layout object&amp;#39;s stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:283</div></div>
<div class="ttc" id="structcutlass_1_1platform_1_1remove__const_html_ac3662947fa50251daf58240a9c798085"><div class="ttname"><a href="structcutlass_1_1platform_1_1remove__const.html#ac3662947fa50251daf58240a9c798085">cutlass::platform::remove_const::type</a></div><div class="ttdeci">T type</div><div class="ttdef"><b>Definition:</b> platform.h:351</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html">cutlass::IdentityTensorLayout</a></div><div class="ttdef"><b>Definition:</b> tensor_ref.h:45</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ac7db3ca62ab1dfe0d3ea08bcadbc9352"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ac7db3ca62ab1dfe0d3ea08bcadbc9352">cutlass::TensorRef&lt; Element, Layout &gt;::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Element * data() const</div><div class="ttdoc">Returns the pointer to referenced data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:254</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_a5e1b58137ca0996e3fa0f727a1d85761"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#a5e1b58137ca0996e3fa0f727a1d85761">cutlass::IdentityTensorLayout::Stride</a></div><div class="ttdeci">Coord&lt; kStrideRank, Index &gt; Stride</div><div class="ttdoc">Stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:63</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a5c5c66a59e9759f11f832fb71f4234c2"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a5c5c66a59e9759f11f832fb71f4234c2">cutlass::TensorRef&lt; Element, Layout &gt;::const_ref</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE ConstTensorRef const_ref() const</div><div class="ttdoc">Returns a reference to constant-valued tensor. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:224</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_ae64fd86033500257785f6923da2558c0"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#ae64fd86033500257785f6923da2558c0">cutlass::IdentityTensorLayout::Index</a></div><div class="ttdeci">int32_t Index</div><div class="ttdoc">Index type used for coordinates. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:54</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ae0e372b28e665820e6a2d17fc9f68d2b"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ae0e372b28e665820e6a2d17fc9f68d2b">cutlass::TensorRef&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Layout</a></div><div class="ttdeci">Layout Layout</div><div class="ttdoc">Mapping function from logical coordinate to linear memory. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:152</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_a041b285984b1940d70a2b7768416e996"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#a041b285984b1940d70a2b7768416e996">cutlass::IdentityTensorLayout::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride &amp; stride()</div><div class="ttdoc">Returns the stride of the layout. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:97</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a1c539841446205c482fe18cc0a99d913"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a1c539841446205c482fe18cc0a99d913">cutlass::TensorRef&lt; Element, Layout &gt;::reset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void reset(Element *ptr, Layout const &amp;layout)</div><div class="ttdoc">Updates the pointer and layout object. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:241</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ac5f24cda8149ddfa8f19467ff2629f49"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ac5f24cda8149ddfa8f19467ff2629f49">cutlass::TensorRef&lt; Element, Layout &gt;::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; operator-=(TensorCoord const &amp;b)</div><div class="ttdoc">Returns a TensorRef offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:356</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a10e36681cae687d9a45ec01bad2626b7"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a10e36681cae687d9a45ec01bad2626b7">cutlass::TensorRef&lt; Element, Layout &gt;::operator[]</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference operator[](TensorCoord const &amp;coord) const</div><div class="ttdoc">Returns a reference to the element at a given Coord. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:313</div></div>
<div class="ttc" id="platform_8h_html"><div class="ttname"><a href="platform_8h.html">platform.h</a></div><div class="ttdoc">C++ features that may be otherwise unimplemented for CUDA device functions. </div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a26c607b811a8f828f5ec5b00f3b874ea"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a26c607b811a8f828f5ec5b00f3b874ea">cutlass::TensorRef&lt; Element, Layout &gt;::TensorRef</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef(Element *ptr=nullptr, Layout const &amp;layout=Layout())</div><div class="ttdoc">Constructs a TensorRef with a pointer and layout object. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:207</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_aca7d373c988016ee946f85ea958b21a7"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#aca7d373c988016ee946f85ea958b21a7">cutlass::TensorRef&lt; Element, Layout &gt;::layout</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Layout layout() const</div><div class="ttdoc">Returns the layout object. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:271</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a4bed879c428963070de8ffbdc5d6e4f9"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a4bed879c428963070de8ffbdc5d6e4f9">cutlass::TensorRef&lt; Element, Layout &gt;::add_coord_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; add_coord_offset(TensorCoord const &amp;coord)</div><div class="ttdoc">Adds an offset to each pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:326</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a6c1be7001a3fb05f8ac2d1fe7ea94c68"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a6c1be7001a3fb05f8ac2d1fe7ea94c68">cutlass::TensorRef&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Element</a></div><div class="ttdeci">Element Element</div><div class="ttdoc">Data type of individual access. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:149</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a965e8b3b7f92dc51d4d3821ea6a25012"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a965e8b3b7f92dc51d4d3821ea6a25012">cutlass::TensorRef&lt; Element, Layout &gt;::data</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference data(LongIndex idx) const</div><div class="ttdoc">Returns a reference to the element at a given linear index. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:258</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_aa275fd660406665884f25c6f693a8892"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#aa275fd660406665884f25c6f693a8892">cutlass::TensorRef&lt; Element, Layout &gt;::operator-</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef operator-(TensorCoord const &amp;b) const</div><div class="ttdoc">Returns a TensorRef offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:348</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a2b79f0d7c0c70774199ba4226bae0fe0"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a2b79f0d7c0c70774199ba4226bae0fe0">cutlass::TensorRef&lt; Element, Layout &gt;::TensorRef</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef(NonConstTensorRef const &amp;ref)</div><div class="ttdoc">Converting constructor from TensorRef to non-constant data. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:217</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a191e88bc0fb310be655d700e937ab97c"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a191e88bc0fb310be655d700e937ab97c">cutlass::TensorRef&lt; Element, Layout &gt;::stride</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Stride stride() const</div><div class="ttdoc">Returns the layout object&amp;#39;s stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:277</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ace218cdb46555a46bd71dbdfc2c317c1"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ace218cdb46555a46bd71dbdfc2c317c1">cutlass::TensorRef&lt; Element, Layout &gt;&lt; Element, Layout &gt;::TensorCoord</a></div><div class="ttdeci">typename Layout::TensorCoord TensorCoord</div><div class="ttdoc">Coordinate in logical tensor space. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:171</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ac968ea9cb34fa99d29c64608c53bd4d4"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ac968ea9cb34fa99d29c64608c53bd4d4">cutlass::TensorRef&lt; Element, Layout &gt;::good</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool good() const</div><div class="ttdoc">Returns true if the TensorRef is non-null. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:248</div></div>
<div class="ttc" id="structcutlass_1_1sizeof__bits_html"><div class="ttname"><a href="structcutlass_1_1sizeof__bits.html">cutlass::sizeof_bits</a></div><div class="ttdoc">Defines the size of an element in bits. </div><div class="ttdef"><b>Definition:</b> numeric_types.h:42</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a9c2149162016bc19c7735b824d57eb9e"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a9c2149162016bc19c7735b824d57eb9e">cutlass::TensorRef&lt; Element, Layout &gt;::reset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void reset(Element *ptr=nullptr)</div><div class="ttdoc">Updates only the pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:235</div></div>
<div class="ttc" id="structcutlass_1_1ReferenceFactory_html"><div class="ttname"><a href="structcutlass_1_1ReferenceFactory.html">cutlass::ReferenceFactory</a></div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:557</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef</a></div><div class="ttdef"><b>Definition:</b> tensor_ref.h:146</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a0aba29ebf8715d217817a49356f95d3f"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a0aba29ebf8715d217817a49356f95d3f">cutlass::TensorRef&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Reference</a></div><div class="ttdeci">typename platform::conditional&lt; sizeof_bits&lt; Element &gt;::value &gt;=8, Element &amp;, SubbyteReference&lt; Element &gt; &gt;::type Reference</div><div class="ttdoc">Reference type to an element. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:159</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a4166ac2a0754574ac21d5d57d74f34e5"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a4166ac2a0754574ac21d5d57d74f34e5">cutlass::TensorRef&lt; Element, Layout &gt;::offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex offset(TensorCoord const &amp;coord) const</div><div class="ttdoc">Computes the offset of an index from the origin of the tensor. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:301</div></div>
<div class="ttc" id="structcutlass_1_1platform_1_1conditional_html"><div class="ttname"><a href="structcutlass_1_1platform_1_1conditional.html">cutlass::platform::conditional</a></div><div class="ttdoc">std::conditional (true specialization) </div><div class="ttdef"><b>Definition:</b> platform.h:325</div></div>
<div class="ttc" id="platform_8h_html_adde4c9ea91b753491851361a4198c009"><div class="ttname"><a href="platform_8h.html#adde4c9ea91b753491851361a4198c009">static_assert</a></div><div class="ttdeci">#define static_assert(__e, __m)</div><div class="ttdef"><b>Definition:</b> platform.h:153</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_a813d116ff0e45679a2a7960a7c10fd1b"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#a813d116ff0e45679a2a7960a7c10fd1b">cutlass::IdentityTensorLayout::kRank</a></div><div class="ttdeci">static int const kRank</div><div class="ttdoc">Logical rank of tensor. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:48</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a66a9bab939e2d57c130bb76a5527f482"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a66a9bab939e2d57c130bb76a5527f482">cutlass::TensorRef&lt; Element, Layout &gt;::non_const_ref</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE NonConstTensorRef non_const_ref() const</div><div class="ttdef"><b>Definition:</b> tensor_ref.h:229</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord</a></div><div class="ttdoc">Statically-sized array specifying Coords within a tensor. </div><div class="ttdef"><b>Definition:</b> coord.h:43</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_aea0b83b611144c3f5860712967234ab4"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#aea0b83b611144c3f5860712967234ab4">cutlass::IdentityTensorLayout::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Long index type used for offsets. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:57</div></div>
<div class="ttc" id="namespacecutlass_html_accfe64331a1403e14cc312d1b4c844e1"><div class="ttname"><a href="namespacecutlass.html#accfe64331a1403e14cc312d1b4c844e1">cutlass::make_TensorRef</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef&lt; Element, Layout &gt; make_TensorRef(Element *ptr, Layout const &amp;layout)</div><div class="ttdoc">Constructs a TensorRef, deducing types from arguments. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:368</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a11ec4b07a2132e647ca2ebe5112ce5ec"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">cutlass::TensorRef&lt; Element, Layout &gt;&lt; Element, Layout &gt;::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdoc">Index type. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:165</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_a2aff6124a25853f227cdd7f3b36733c4"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#a2aff6124a25853f227cdd7f3b36733c4">cutlass::IdentityTensorLayout::IdentityTensorLayout</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE IdentityTensorLayout(Stride const &amp;stride=Stride())</div><div class="ttdef"><b>Definition:</b> tensor_ref.h:81</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a8758907a1c9b1fcd00e7ece626d03b76"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">cutlass::TensorRef&lt; Element, Layout &gt;::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference at(TensorCoord const &amp;coord) const</div><div class="ttdoc">Returns a reference to the element at a given Coord. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:307</div></div>
<div class="ttc" id="classcutlass_1_1SubbyteReference_html"><div class="ttname"><a href="classcutlass_1_1SubbyteReference.html">cutlass::SubbyteReference</a></div><div class="ttdef"><b>Definition:</b> subbyte_reference.h:294</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a7771fcd932e36c51c15572305f5b5520"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a7771fcd932e36c51c15572305f5b5520">cutlass::TensorRef&lt; Element, Layout &gt;::layout</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Layout &amp; layout()</div><div class="ttdoc">Returns the layout object. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:265</div></div>
<div class="ttc" id="namespacecutlass_html_aa43b0a7d59635cb2d9ac96a077c988c3"><div class="ttname"><a href="namespacecutlass.html#aa43b0a7d59635cb2d9ac96a077c988c3">cutlass::TensorRef_aligned</a></div><div class="ttdeci">bool TensorRef_aligned(TensorRef&lt; Element, Layout &gt; const &amp;ref, int alignment)</div><div class="ttdef"><b>Definition:</b> tensor_ref.h:382</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ad4e1a9d4912a18547cd5391d63e8e7ac"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ad4e1a9d4912a18547cd5391d63e8e7ac">cutlass::TensorRef&lt; Element, Layout &gt;::operator+</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef operator+(TensorCoord const &amp;b) const</div><div class="ttdoc">Returns a TensorRef offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:333</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_ae82b8ba3bbf7ebb28fe063932db3dc6b"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#ae82b8ba3bbf7ebb28fe063932db3dc6b">cutlass::TensorRef&lt; Element, Layout &gt;::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; operator+=(TensorCoord const &amp;b)</div><div class="ttdoc">Returns a TensorRef offset by a given amount. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:341</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a6bbcd0e512915565cabfeccdb1b6417d"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a6bbcd0e512915565cabfeccdb1b6417d">cutlass::TensorRef&lt; Element, Layout &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorRef &amp; add_pointer_offset(LongIndex offset_)</div><div class="ttdoc">Adds an offset to each pointer. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:319</div></div>
<div class="ttc" id="subbyte__reference_8h_html"><div class="ttname"><a href="subbyte__reference_8h.html">subbyte_reference.h</a></div><div class="ttdoc">Provides a mechanism for packing and unpacking elements smaller than one byte. </div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_ad0ed9dc11a6284d25ea588d6933d2965"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#ad0ed9dc11a6284d25ea588d6933d2965">cutlass::IdentityTensorLayout::kStrideRank</a></div><div class="ttdeci">static int const kStrideRank</div><div class="ttdoc">Rank of stride vector. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:51</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_a057a417a4d4a6e2f69e0b55a6f7ee902"><div class="ttname"><a href="structcutlass_1_1Coord.html#a057a417a4d4a6e2f69e0b55a6f7ee902">cutlass::Coord::dot</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex dot(Coord const &amp;b, LongIndex sum=LongIndex(0)) const </div><div class="ttdoc">Computes the dot product with anotherCoord object. </div><div class="ttdef"><b>Definition:</b> coord.h:246</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_abe58b7c8f153a6029c2adc173f340fe0"><div class="ttname"><a href="structcutlass_1_1Coord.html#abe58b7c8f153a6029c2adc173f340fe0">cutlass::Coord::max_dim_index</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE int max_dim_index() const </div><div class="ttdoc">Returns the index of the dimension with greatest value. </div><div class="ttdef"><b>Definition:</b> coord.h:130</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_adeada5e33b231f125a4aaeaf963bd3a3"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">cutlass::TensorRef&lt; Element, Layout &gt;&lt; Element, Layout &gt;::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdoc">Long index used for pointer offsets. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:168</div></div>
<div class="ttc" id="classcutlass_1_1IdentityTensorLayout_html_a3dc530520b5eb35bc57c75de7954b59f"><div class="ttname"><a href="classcutlass_1_1IdentityTensorLayout.html#a3dc530520b5eb35bc57c75de7954b59f">cutlass::IdentityTensorLayout::capacity</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE LongIndex capacity(TensorCoord const &amp;size) const </div><div class="ttdoc">Compute the number of contiguous elements needed to store a tensor with the given size...</div><div class="ttdef"><b>Definition:</b> tensor_ref.h:103</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
