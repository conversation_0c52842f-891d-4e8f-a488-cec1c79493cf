<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::PitchLinearWarpStripedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">PitchLinearWarpStripedThreadMap</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::PitchLinearWarpStripedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html">Detail</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal details made public to facilitate introspection Iterations along each dimension (concept: PitchLinearShape)  <a href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:affc28140360a50701f9e26981804c12d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#affc28140360a50701f9e26981804c12d">TensorCoord</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td></tr>
<tr class="memdesc:affc28140360a50701f9e26981804c12d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor coordinate.  <a href="#affc28140360a50701f9e26981804c12d">More...</a><br /></td></tr>
<tr class="separator:affc28140360a50701f9e26981804c12d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aba9ddc00e9374a762500aaa10143d872"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#aba9ddc00e9374a762500aaa10143d872">Shape</a> = Shape_</td></tr>
<tr class="memdesc:aba9ddc00e9374a762500aaa10143d872"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile shape.  <a href="#aba9ddc00e9374a762500aaa10143d872">More...</a><br /></td></tr>
<tr class="separator:aba9ddc00e9374a762500aaa10143d872"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3769c9fec5e9937201dbba0e89b171ec"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a3769c9fec5e9937201dbba0e89b171ec">ThreadAccessShape</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858">kElementsPerAccess</a>, 1 &gt;</td></tr>
<tr class="memdesc:a3769c9fec5e9937201dbba0e89b171ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shape of access by each thread.  <a href="#a3769c9fec5e9937201dbba0e89b171ec">More...</a><br /></td></tr>
<tr class="separator:a3769c9fec5e9937201dbba0e89b171ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a967b87df4c12d47bc73594745efc570d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a967b87df4c12d47bc73594745efc570d">Iterations</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::WarpAccessIterations::kContiguous</a>/<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a363250282dfea2d4c3c42500b6fb90e9">Detail::kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::WarpAccessIterations::kStrided</a>/<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a20bc67607c17bf1fc367a6b3a3bb6ad3">Detail::kWarpsStrided</a> &gt;</td></tr>
<tr class="separator:a967b87df4c12d47bc73594745efc570d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8c898ce4cdfff0eb223534c0607eb9e3"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8c898ce4cdfff0eb223534c0607eb9e3">Delta</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Detail::WarpThreadArrangement::kContiguous *<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858">kElementsPerAccess</a>, Detail::WarpThreadArrangement::kStrided *<a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::WarpArrangement::kStrided</a> &gt;</td></tr>
<tr class="memdesc:a8c898ce4cdfff0eb223534c0607eb9e3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Delta betweeen accesses (units of elements, concept: PitchLinearShape)  <a href="#a8c898ce4cdfff0eb223534c0607eb9e3">More...</a><br /></td></tr>
<tr class="separator:a8c898ce4cdfff0eb223534c0607eb9e3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:acc6d9ccbb71792f3dea2e7c6c88f3a07"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#affc28140360a50701f9e26981804c12d">TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#acc6d9ccbb71792f3dea2e7c6c88f3a07">initial_offset</a> (int thread_id)</td></tr>
<tr class="memdesc:acc6d9ccbb71792f3dea2e7c6c88f3a07"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maps thread ID to a coordinate offset within the tensor's logical coordinate space.  <a href="#acc6d9ccbb71792f3dea2e7c6c88f3a07">More...</a><br /></td></tr>
<tr class="separator:acc6d9ccbb71792f3dea2e7c6c88f3a07"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a58fa53118303ef62f8b7557616606f8f"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a58fa53118303ef62f8b7557616606f8f">kThreads</a> = Threads</td></tr>
<tr class="memdesc:a58fa53118303ef62f8b7557616606f8f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads total.  <a href="#a58fa53118303ef62f8b7557616606f8f">More...</a><br /></td></tr>
<tr class="separator:a58fa53118303ef62f8b7557616606f8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d3b364145189790a0e5fa033d63f858"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858">kElementsPerAccess</a> = ElementsPerAccess</td></tr>
<tr class="memdesc:a8d3b364145189790a0e5fa033d63f858"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract vector length from Layout.  <a href="#a8d3b364145189790a0e5fa033d63f858">More...</a><br /></td></tr>
<tr class="separator:a8d3b364145189790a0e5fa033d63f858"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename Shape_, int Threads, typename WarpThreadArrangement_, int ElementsPerAccess = 1&gt;<br />
struct cutlass::transform::PitchLinearWarpStripedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;</h3>

<p>Policy defining a warp-striped arrangement. This partitions a tile into vectorized memory accesses performed by each warp then distributes warps across them. Warps are striped in the strided dimension and raked across the contiguous dimension. </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a8c898ce4cdfff0eb223534c0607eb9e3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8c898ce4cdfff0eb223534c0607eb9e3">Delta</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Detail::WarpThreadArrangement::kContiguous * <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858">kElementsPerAccess</a>, Detail::WarpThreadArrangement::kStrided * <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::WarpArrangement::kStrided</a> &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a967b87df4c12d47bc73594745efc570d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a967b87df4c12d47bc73594745efc570d">Iterations</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::WarpAccessIterations::kContiguous</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a363250282dfea2d4c3c42500b6fb90e9">Detail::kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::WarpAccessIterations::kStrided</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a20bc67607c17bf1fc367a6b3a3bb6ad3">Detail::kWarpsStrided</a> &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aba9ddc00e9374a762500aaa10143d872"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#aba9ddc00e9374a762500aaa10143d872">Shape</a> =  Shape_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="affc28140360a50701f9e26981804c12d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#affc28140360a50701f9e26981804c12d">TensorCoord</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3769c9fec5e9937201dbba0e89b171ec"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a3769c9fec5e9937201dbba0e89b171ec">ThreadAccessShape</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858">kElementsPerAccess</a>, 1&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="acc6d9ccbb71792f3dea2e7c6c88f3a07"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#affc28140360a50701f9e26981804c12d">TensorCoord</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::initial_offset </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>thread_id</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a8d3b364145189790a0e5fa033d63f858"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::kElementsPerAccess = ElementsPerAccess</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a58fa53118303ef62f8b7557616606f8f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::kThreads = Threads</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
