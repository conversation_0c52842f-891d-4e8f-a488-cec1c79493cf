<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::device::detail::TensorFillDiagonalFunc&lt; Element, Layout &gt;::Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device.html">device</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html">TensorFillDiagonalFunc</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params.html">Params</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::device::detail::TensorFillDiagonalFunc&lt; Element, Layout &gt;::Params Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Parameters structure.  
</p>

<p><code>#include &lt;<a class="el" href="device_2tensor__fill_8h_source.html">tensor_fill.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::device::detail::TensorFillDiagonalFunc&lt; Element, Layout &gt;::Params:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_3_01Element_00_01Layout_01_4_1_1Params_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_3_01Element_00_01Layout_01_4_1_1Params_coll__map" id="cutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_3_01Element_00_01Layout_01_4_1_1Params_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a9e5512d91acbfdcf4bc74d029b7a93e7"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params.html#a9e5512d91acbfdcf4bc74d029b7a93e7">Params</a> ()</td></tr>
<tr class="memdesc:a9e5512d91acbfdcf4bc74d029b7a93e7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default ctor.  <a href="#a9e5512d91acbfdcf4bc74d029b7a93e7">More...</a><br /></td></tr>
<tr class="separator:a9e5512d91acbfdcf4bc74d029b7a93e7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a84d5f8e16088096ed658b4226ba36b8c"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params.html#a84d5f8e16088096ed658b4226ba36b8c">Params</a> (<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html#a79e4b8a8edb8872c1612e6035b1ab25f">TensorView</a> view_=<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html#a79e4b8a8edb8872c1612e6035b1ab25f">TensorView</a>(), Element diag_=Element(1), Element other_=Element(0))</td></tr>
<tr class="memdesc:a84d5f8e16088096ed658b4226ba36b8c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Construction of Gaussian RNG functor.  <a href="#a84d5f8e16088096ed658b4226ba36b8c">More...</a><br /></td></tr>
<tr class="separator:a84d5f8e16088096ed658b4226ba36b8c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:aa75cbbb80fdd96f99fe81cad9427ac2a"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html#a79e4b8a8edb8872c1612e6035b1ab25f">TensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params.html#aa75cbbb80fdd96f99fe81cad9427ac2a">view</a></td></tr>
<tr class="separator:aa75cbbb80fdd96f99fe81cad9427ac2a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abcbca40684cd478a53c0cc80c8e418e1"><td class="memItemLeft" align="right" valign="top">Element&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params.html#abcbca40684cd478a53c0cc80c8e418e1">diag</a></td></tr>
<tr class="separator:abcbca40684cd478a53c0cc80c8e418e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a4b44e8cc5d91ef6e388a17a741ca130a"><td class="memItemLeft" align="right" valign="top">Element&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc_1_1Params.html#a4b44e8cc5d91ef6e388a17a741ca130a">other</a></td></tr>
<tr class="separator:a4b44e8cc5d91ef6e388a17a741ca130a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a9e5512d91acbfdcf4bc74d029b7a93e7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html">cutlass::reference::device::detail::TensorFillDiagonalFunc</a>&lt; Element, Layout &gt;::Params::Params </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a84d5f8e16088096ed658b4226ba36b8c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html">cutlass::reference::device::detail::TensorFillDiagonalFunc</a>&lt; Element, Layout &gt;::Params::Params </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html#a79e4b8a8edb8872c1612e6035b1ab25f">TensorView</a>&#160;</td>
          <td class="paramname"><em>view_</em> = <code><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html#a79e4b8a8edb8872c1612e6035b1ab25f">TensorView</a>()</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Element&#160;</td>
          <td class="paramname"><em>diag_</em> = <code>Element(1)</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Element&#160;</td>
          <td class="paramname"><em>other_</em> = <code>Element(0)</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="abcbca40684cd478a53c0cc80c8e418e1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">Element <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html">cutlass::reference::device::detail::TensorFillDiagonalFunc</a>&lt; Element, Layout &gt;::Params::diag</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4b44e8cc5d91ef6e388a17a741ca130a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">Element <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html">cutlass::reference::device::detail::TensorFillDiagonalFunc</a>&lt; Element, Layout &gt;::Params::other</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa75cbbb80fdd96f99fe81cad9427ac2a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html#a79e4b8a8edb8872c1612e6035b1ab25f">TensorView</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorFillDiagonalFunc.html">cutlass::reference::device::detail::TensorFillDiagonalFunc</a>&lt; Element, Layout &gt;::Params::view</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="device_2tensor__fill_8h_source.html">device/tensor_fill.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
