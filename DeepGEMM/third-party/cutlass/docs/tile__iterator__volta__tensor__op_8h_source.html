<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tile_iterator_volta_tensor_op.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tile_iterator_volta_tensor_op.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tile__iterator__volta__tensor__op_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="pitch__linear_8h.html">cutlass/layout/pitch_linear.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="tensor__op__policy_8h.html">cutlass/epilogue/warp/tensor_op_policy.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  <span class="keyword">typename</span> WarpShape,             </div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keyword">typename</span> InterleavedTileShape,  </div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">typename</span> ElementC,              </div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;  <span class="keyword">typename</span> Layout                 </div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;&gt;</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">   52</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a>; </div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;  <span class="keyword">typename</span> WarpShape_         </div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;&gt;</div><div class="line"><a name="l00060"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html">   60</a></span>&#160;<span class="keyword">class </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a>&lt;WarpShape_, gemm::GemmShape&lt;32, 32, 4&gt;, <a class="code" href="structcutlass_1_1half__t.html">half_t</a>, <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>&gt; {</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a5d55a2353bb364861072abade9fb34a5">   63</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a5d55a2353bb364861072abade9fb34a5">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#aa505394127d3158e94d6b9b7a002056b">   64</a></span>&#160;  <span class="keyword">using</span> InterleavedTileShape = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;32, 32, 4&gt;</a>;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#af99aa8698689b4d0ba67c84cd42d0db7">   65</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1half__t.html">Element</a> = <a class="code" href="structcutlass_1_1half__t.html">half_t</a>;</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a4a162c8119b397c927535a1d985e3e06">   66</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a8af85c5711b2d82873479be71b796f48">   68</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;         </div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#aca35f67bd971f13b4416cede6633074b">   69</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;                      </div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a3c929d73e2d8f8fba358fab7eaf3b91b">   70</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a3c929d73e2d8f8fba358fab7eaf3b91b">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">TensorRef::Index</a>;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#acca8fbc04b64161456135dae7f44d40c">   71</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#acca8fbc04b64161456135dae7f44d40c">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">TensorRef::LongIndex</a>;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ac25c73990d9bb57ccaaa536b5569490f">   73</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">VoltaTensorOpPolicy&lt;WarpShape, InterleavedTileShape, Element, Layout&gt;</a>;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Shape</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;    Policy::kRowsPerIteration,</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;    WarpShape::kN</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ac4b5f883e536173995ce5ab9d452eb6b">   79</a></span>&#160;  &gt;;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">   82</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> = <span class="keyword">typename</span> Policy::AccessType;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  </div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a77d5d1e5e8af463d1ec30bfee41f1161">   85</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a77d5d1e5e8af463d1ec30bfee41f1161">Fragment</a> = <span class="keyword">typename</span> Policy::Fragment;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a8a79ba0606d52670402e37ee2e8d8a19">   88</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a8a79ba0606d52670402e37ee2e8d8a19">AccumulatorTile</a> = <span class="keyword">typename</span> Policy::AccumulatorTile;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a1be924f14d935345299d2c6bbddbf913">   91</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a0345cdb103d294f0df1a34fd343b92d7">   94</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Policy::kElementsPerAccess;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;  <span class="comment">// Internal constants</span></div><div class="line"><a name="l00097"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html">   97</a></span>&#160;  <span class="keyword">struct </span>Detail {</div><div class="line"><a name="l00098"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a48cbbc34bbcea59e18a0456456d95647">   98</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kLanesInQuad = 4;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a119c2b2e771aed34ad6b8ac05730e352">   99</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerQuad = 4;</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#ab055e705c6aa732223e21b9498dfa3f8">  100</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kColumnsPerQuad = 8;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a1bf53fa314f37d678046132d398f5124">  101</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessesPerQuad = kColumnsPerQuad / Policy::kElementsPerAccess;</div><div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemmffcab2297c8de8d0013602a39c525b78.html#a9f9d4ce2c61db3266a4c58876ed697ee">  102</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessQuadDelta = 16;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  };</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Padding</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;    0,</div><div class="line"><a name="l00108"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a516c1e4268bc2629c3539a995963ffe4">  108</a></span>&#160;    Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> *pointer_;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> layout_;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#adf36e421cb4b5b01b465f0417bc931b2">  126</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#adf36e421cb4b5b01b465f0417bc931b2">TileIteratorVoltaTensorOp</a>(): pointer_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { }</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00130"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a7bf6d1c658c57c0112722b638c17cc58">  130</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a7bf6d1c658c57c0112722b638c17cc58">TileIteratorVoltaTensorOp</a>(</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <span class="keyword">const</span> &amp;ref,</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;    <span class="keywordtype">unsigned</span> lane_id</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;  ):</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    pointer_(reinterpret_cast&lt;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> *&gt;(ref.data())),</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;    layout_(ref.stride()[0] / <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">Policy</a>::kElementsPerAccess) { </div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;    <span class="keywordtype">int</span> quad_id = lane_id / Detail::kLanesInQuad;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;    <span class="keywordtype">int</span> lane_in_quad = (lane_id % Detail::kLanesInQuad);</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <span class="keywordtype">int</span> quad_row_idx = ((quad_id &amp; 4) &gt;&gt; 1) + (quad_id &amp; 1);</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="keywordtype">int</span> quad_col_idx = ((quad_id &amp; 2) &gt;&gt; 1);</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;    <span class="keywordtype">int</span> row = quad_row_idx * Detail::kRowsPerQuad + lane_in_quad;</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;    <span class="keywordtype">int</span> column = quad_col_idx * Detail::kColumnsPerQuad;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;    pointer_ += layout_({row, column / kElementsPerAccess});</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;  }</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00151"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#aa5aec99eff41c3c17177c792bbecb1ca">  151</a></span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#aa5aec99eff41c3c17177c792bbecb1ca">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a3c929d73e2d8f8fba358fab7eaf3b91b">Index</a> pointer_offset) {</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;    pointer_ += pointer_offset / Policy::kElementsPerAccess;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;  }</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00158"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ab9627099133cc3fad152ea9984458147">  158</a></span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ab9627099133cc3fad152ea9984458147">add_tile_offset</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;    pointer_ += layout_({</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;      tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * Shape::kRow, </div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;      tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * Shape::kColumn / Policy::kElementsPerAccess});</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  }</div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a10e475f4cadd7ec7371c5b74742a6081">  169</a></span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a10e475f4cadd7ec7371c5b74742a6081">operator+=</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;    add_tile_offset(tile_offset);</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  }</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00176"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a5e3d6ed3bfdea5c21d74ff1d73570ced">  176</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a5e3d6ed3bfdea5c21d74ff1d73570ced">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a77d5d1e5e8af463d1ec30bfee41f1161">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a3c929d73e2d8f8fba358fab7eaf3b91b">Index</a> pointer_offset) {</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> <span class="keyword">const</span> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> tile_idx = 0; tile_idx &lt; Policy::TileIterations::kColumn; ++tile_idx) {</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> access_idx = 0; access_idx &lt; Policy::kAccessesPerInterleavedTile; ++access_idx) {</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;        <span class="keywordtype">int</span> access_quad = access_idx / 2;</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;        <span class="keywordtype">int</span> access = access_idx % 2;</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        <span class="keywordtype">int</span> ptr_offset = tile_idx * InterleavedTileShape::kN / Policy::kElementsPerAccess +</div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;          access_quad * Detail::kAccessQuadDelta / Policy::kElementsPerAccess  + access;</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;        <span class="keywordtype">int</span> frag_idx = tile_idx * Policy::kAccessesPerInterleavedTile + access_idx;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;</div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;        <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> access_vector = frag_ptr[frag_idx];</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;        pointer_[ptr_offset] = access_vector;</div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;      }</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;    }</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;  }</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00203"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a9c9ffc57742ab63f9bd943a33746461d">  203</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a9c9ffc57742ab63f9bd943a33746461d">store</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a77d5d1e5e8af463d1ec30bfee41f1161">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;    store_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;  }</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ac192d6b2cd73d09ef1bd275fb58666e0">  209</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ac192d6b2cd73d09ef1bd275fb58666e0">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a77d5d1e5e8af463d1ec30bfee41f1161">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a3c929d73e2d8f8fba358fab7eaf3b91b">Index</a> pointer_offset) {</div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> tile_idx = 0; tile_idx &lt; Policy::TileIterations::kColumn; ++tile_idx) {</div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> access_idx = 0; access_idx &lt; Policy::kAccessesPerInterleavedTile; ++access_idx) {</div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        <span class="keywordtype">int</span> access_quad = access_idx / 2;</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        <span class="keywordtype">int</span> access = access_idx % 2;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;        <span class="keywordtype">int</span> ptr_offset = tile_idx * Detail::kTileDelta + access_quad * Detail::kAccessQuadDelta + access;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;        <span class="keywordtype">int</span> frag_idx = tile_idx * Policy::kAccessesPerInterleavedTile + access_idx;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        frag_ptr[frag_idx] = pointer_[ptr_offset];</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;      }</div><div class="line"><a name="l00227"></a><span class="lineno">  227</span>&#160;    }</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;  }</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00232"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a7e2587da22e6d5273fcd33e43a76ff97">  232</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a7e2587da22e6d5273fcd33e43a76ff97">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a77d5d1e5e8af463d1ec30bfee41f1161">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;    load_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  }</div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;};</div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  <span class="keyword">typename</span> WarpShape_         </div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;&gt;</div><div class="line"><a name="l00243"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html">  243</a></span>&#160;<span class="keyword">class </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a>&lt;WarpShape_, gemm::GemmShape&lt;32, 32, 4&gt;, float, <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>&gt; {</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00246"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a2a80643f2c5c65154827bec32489ea78">  246</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a2a80643f2c5c65154827bec32489ea78">WarpShape</a> = WarpShape_;</div><div class="line"><a name="l00247"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a1d3db56c73a8f6d7e2be7be014ccc231">  247</a></span>&#160;  <span class="keyword">using</span> InterleavedTileShape = <a class="code" href="structcutlass_1_1gemm_1_1GemmShape.html">gemm::GemmShape&lt;32, 32, 4&gt;</a>;</div><div class="line"><a name="l00248"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a5f4dc6e511ec485c9b03b6fe3d5cea64">  248</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a5f4dc6e511ec485c9b03b6fe3d5cea64">Element</a> = float;</div><div class="line"><a name="l00249"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ae9f78285e55b5b2ff53fe69b24852660">  249</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> = <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">layout::RowMajor</a>;</div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;</div><div class="line"><a name="l00251"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a5c917cd24be6659a3d09068c7dfa70ec">  251</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> = <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;Element, Layout&gt;</a>;         </div><div class="line"><a name="l00252"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a7f60f6f950cc2e1a14fce26a9f861a58">  252</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> = <a class="code" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>;                      </div><div class="line"><a name="l00253"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a1d327b8913b60b3604924d697ebbaf6a">  253</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a1d327b8913b60b3604924d697ebbaf6a">Index</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">TensorRef::Index</a>;</div><div class="line"><a name="l00254"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a5d111ad4fdbb7dfde82edb39b8d90cbc">  254</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a5d111ad4fdbb7dfde82edb39b8d90cbc">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">TensorRef::LongIndex</a>;</div><div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;</div><div class="line"><a name="l00256"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ad011e908ce60c548082a985a2c896f39">  256</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">Policy</a> = <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">VoltaTensorOpPolicy&lt;WarpShape, InterleavedTileShape, Element, Layout&gt;</a>;</div><div class="line"><a name="l00257"></a><span class="lineno">  257</span>&#160;</div><div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Shape</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;    Policy::kRowsPerIteration,</div><div class="line"><a name="l00261"></a><span class="lineno">  261</span>&#160;    WarpShape::kN</div><div class="line"><a name="l00262"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ac86885406a18093868941eb11ece84f7">  262</a></span>&#160;  &gt;;</div><div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;</div><div class="line"><a name="l00265"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">  265</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">AccessType</a> = <span class="keyword">typename</span> Policy::AccessType;</div><div class="line"><a name="l00266"></a><span class="lineno">  266</span>&#160;  </div><div class="line"><a name="l00268"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#afc5b8faf449e4bd922e017b7c5e47fc4">  268</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#afc5b8faf449e4bd922e017b7c5e47fc4">Fragment</a> = <span class="keyword">typename</span> Policy::Fragment;</div><div class="line"><a name="l00269"></a><span class="lineno">  269</span>&#160;</div><div class="line"><a name="l00271"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a30e13f9b73d66049f8233998f29823c4">  271</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a30e13f9b73d66049f8233998f29823c4">AccumulatorTile</a> = <span class="keyword">typename</span> Policy::AccumulatorTile;</div><div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div><div class="line"><a name="l00274"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a54036a133462ef407b56ef9ab7e27033">  274</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = Policy::kIterations;</div><div class="line"><a name="l00275"></a><span class="lineno">  275</span>&#160;</div><div class="line"><a name="l00277"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a70793652078daffd36bf3ef14f1f15d4">  277</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = Policy::kElementsPerAccess;</div><div class="line"><a name="l00278"></a><span class="lineno">  278</span>&#160;</div><div class="line"><a name="l00279"></a><span class="lineno">  279</span>&#160;  <span class="comment">// Internal constants</span></div><div class="line"><a name="l00280"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html">  280</a></span>&#160;  <span class="keyword">struct </span>Detail {</div><div class="line"><a name="l00281"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#ad96cb0ceb99280d669956d3bfb1bfb2e">  281</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kLanesInQuad = 4;</div><div class="line"><a name="l00282"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#a3d20f92ee0eefe511c9d43e67677cd13">  282</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerQuad = 4;</div><div class="line"><a name="l00283"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#ae38ff98b5bc1c9ad217fa5d4a45f55d5">  283</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kColumnsPerQuad = 8;</div><div class="line"><a name="l00284"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#a5ec1c03e8642655de409830ba9d28aac">  284</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessesPerQuad = kColumnsPerQuad / Policy::kElementsPerAccess;</div><div class="line"><a name="l00285"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1Gemm770cbca45441d295d5d7433e8222a700.html#a90cd832bb0f9bc8d7d77181c24a9d782">  285</a></span>&#160;    <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessQuadDelta = 16;</div><div class="line"><a name="l00286"></a><span class="lineno">  286</span>&#160;  };</div><div class="line"><a name="l00287"></a><span class="lineno">  287</span>&#160;</div><div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">Padding</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;    0,</div><div class="line"><a name="l00291"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a3f3dc7225c2cb2f44e5257ad4b3d8b31">  291</a></span>&#160;    Policy::kElementsPerAccess&gt;;</div><div class="line"><a name="l00292"></a><span class="lineno">  292</span>&#160;</div><div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;<span class="keyword">private</span>:</div><div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div><div class="line"><a name="l00295"></a><span class="lineno">  295</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00296"></a><span class="lineno">  296</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;</div><div class="line"><a name="l00300"></a><span class="lineno">  300</span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">AccessType</a> *pointer_;</div><div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;</div><div class="line"><a name="l00303"></a><span class="lineno">  303</span>&#160;  <a class="code" href="classcutlass_1_1layout_1_1RowMajor.html">Layout</a> layout_;</div><div class="line"><a name="l00304"></a><span class="lineno">  304</span>&#160;</div><div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;<span class="keyword">public</span>:</div><div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div><div class="line"><a name="l00308"></a><span class="lineno">  308</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00309"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a434b6974573b635cb4d8e3dc4d3dea74">  309</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a434b6974573b635cb4d8e3dc4d3dea74">TileIteratorVoltaTensorOp</a>(): pointer_(<a class="code" href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a>) { }</div><div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;</div><div class="line"><a name="l00312"></a><span class="lineno">  312</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00313"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a20f1b1e2a9bea03484cb709670ac4308">  313</a></span>&#160;  <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a20f1b1e2a9bea03484cb709670ac4308">TileIteratorVoltaTensorOp</a>(</div><div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;    <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef</a> <span class="keyword">const</span> &amp;ref,</div><div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;    <span class="keywordtype">unsigned</span> lane_id</div><div class="line"><a name="l00316"></a><span class="lineno">  316</span>&#160;  ):</div><div class="line"><a name="l00317"></a><span class="lineno">  317</span>&#160;    pointer_(reinterpret_cast&lt;<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">AccessType</a> *&gt;(ref.data())),</div><div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;    layout_(ref.stride()[0] / <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">Policy</a>::kElementsPerAccess) { </div><div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div><div class="line"><a name="l00320"></a><span class="lineno">  320</span>&#160;    <span class="keywordtype">int</span> quad_id = lane_id / Detail::kLanesInQuad;</div><div class="line"><a name="l00321"></a><span class="lineno">  321</span>&#160;    <span class="keywordtype">int</span> lane_in_quad = (lane_id % Detail::kLanesInQuad);</div><div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;</div><div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kQuadRowDelta = 4;</div><div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kQuadColumnDelta = 2 * Policy::MmaIterations::kColumn;</div><div class="line"><a name="l00325"></a><span class="lineno">  325</span>&#160;</div><div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;    <span class="keywordtype">int</span> quad_row_offset = ((quad_id &amp; 4) / 2 + (quad_id &amp; 1)) * kQuadRowDelta;</div><div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;    <span class="keywordtype">int</span> quad_column_offset = (quad_id &amp; 2) / 2 * kQuadColumnDelta;</div><div class="line"><a name="l00328"></a><span class="lineno">  328</span>&#160;</div><div class="line"><a name="l00329"></a><span class="lineno">  329</span>&#160;    <span class="keywordtype">int</span> thread_row_offset = (lane_in_quad &amp; 1);</div><div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;    <span class="keywordtype">int</span> thread_column_offset = (lane_in_quad &amp; 2) / 2;</div><div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;</div><div class="line"><a name="l00332"></a><span class="lineno">  332</span>&#160;    <span class="keywordtype">int</span> row = quad_row_offset + thread_row_offset;</div><div class="line"><a name="l00333"></a><span class="lineno">  333</span>&#160;    <span class="keywordtype">int</span> column = quad_column_offset + thread_column_offset;</div><div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;</div><div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;    pointer_ += layout_({row, column});</div><div class="line"><a name="l00336"></a><span class="lineno">  336</span>&#160;  }</div><div class="line"><a name="l00337"></a><span class="lineno">  337</span>&#160;</div><div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00340"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a565c2563388775804227ccf9c96c4dd4">  340</a></span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a565c2563388775804227ccf9c96c4dd4">add_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a1d327b8913b60b3604924d697ebbaf6a">Index</a> pointer_offset) {</div><div class="line"><a name="l00341"></a><span class="lineno">  341</span>&#160;    pointer_ += pointer_offset / Policy::kElementsPerAccess;</div><div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;  }</div><div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;</div><div class="line"><a name="l00346"></a><span class="lineno">  346</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00347"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a13ef705330675c5360e4430a9a002c07">  347</a></span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a13ef705330675c5360e4430a9a002c07">add_tile_offset</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;</div><div class="line"><a name="l00349"></a><span class="lineno">  349</span>&#160;    pointer_ += layout_({</div><div class="line"><a name="l00350"></a><span class="lineno">  350</span>&#160;      tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">row</a>() * Shape::kRow, </div><div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;      tile_offset.<a class="code" href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">column</a>() * Shape::kColumn / Policy::kElementsPerAccess});</div><div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;</div><div class="line"><a name="l00353"></a><span class="lineno">  353</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00354"></a><span class="lineno">  354</span>&#160;  }</div><div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;</div><div class="line"><a name="l00357"></a><span class="lineno">  357</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00358"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ac043e1542408af1268bef2b8a09ed7d9">  358</a></span>&#160;  <a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">TileIteratorVoltaTensorOp</a> &amp; <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ac043e1542408af1268bef2b8a09ed7d9">operator+=</a>(<a class="code" href="structcutlass_1_1MatrixCoord.html">TensorCoord</a> <span class="keyword">const</span> &amp;tile_offset) {</div><div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;    add_tile_offset(tile_offset);</div><div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;  }</div><div class="line"><a name="l00362"></a><span class="lineno">  362</span>&#160;</div><div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;  CUTLASS_DEVICE</div><div class="line"><a name="l00365"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ad2c00ec8815e91cf06491832a0ab7b9b">  365</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ad2c00ec8815e91cf06491832a0ab7b9b">store_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#afc5b8faf449e4bd922e017b7c5e47fc4">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a1d327b8913b60b3604924d697ebbaf6a">Index</a> pointer_offset) {</div><div class="line"><a name="l00366"></a><span class="lineno">  366</span>&#160;</div><div class="line"><a name="l00367"></a><span class="lineno">  367</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">AccessType</a> <span class="keyword">const</span> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">AccessType</a> <span class="keyword">const </span>*<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;</div><div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;    <span class="keywordtype">int</span> <span class="keyword">const</span> kAccessesPerRow = Policy::TileIterations::kColumn * Policy::MmaIterations::kColumn * 2;</div><div class="line"><a name="l00370"></a><span class="lineno">  370</span>&#160;</div><div class="line"><a name="l00371"></a><span class="lineno">  371</span>&#160;    <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;    <span class="keywordflow">for</span> (<span class="keywordtype">int</span> row_idx = 0; row_idx &lt; Policy::kRowsPerMmaTile; ++row_idx) {</div><div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div><div class="line"><a name="l00374"></a><span class="lineno">  374</span>&#160;      <a class="code" href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="line"><a name="l00375"></a><span class="lineno">  375</span>&#160;      <span class="keywordflow">for</span> (<span class="keywordtype">int</span> access_idx = 0; access_idx &lt; kAccessesPerRow; ++access_idx) {</div><div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;</div><div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;        <span class="keywordtype">int</span> frag_idx = row_idx * kAccessesPerRow + access_idx;</div><div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;</div><div class="line"><a name="l00379"></a><span class="lineno">  379</span>&#160;        <span class="keywordtype">int</span> ptr_column_offset = (access_idx &amp; 1) * 2 + </div><div class="line"><a name="l00380"></a><span class="lineno">  380</span>&#160;          (access_idx &amp; 2) * Policy::MmaIterations::kColumn * 2 + </div><div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;          (access_idx &amp; 4) * Policy::MmaIterations::kColumn * 2;</div><div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;</div><div class="line"><a name="l00383"></a><span class="lineno">  383</span>&#160;        <span class="keywordtype">int</span> ptr_row_offset = row_idx * 2;</div><div class="line"><a name="l00384"></a><span class="lineno">  384</span>&#160;</div><div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;        <span class="keywordtype">int</span> ptr_offset = layout_({ptr_row_offset, ptr_column_offset});</div><div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;</div><div class="line"><a name="l00387"></a><span class="lineno">  387</span>&#160;        pointer_[ptr_offset] = frag_ptr[frag_idx];</div><div class="line"><a name="l00388"></a><span class="lineno">  388</span>&#160;      }</div><div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;    }</div><div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;  }</div><div class="line"><a name="l00391"></a><span class="lineno">  391</span>&#160;</div><div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00394"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a0a8719a2339a6eda6dc1f2f70fae8aea">  394</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a0a8719a2339a6eda6dc1f2f70fae8aea">store</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#afc5b8faf449e4bd922e017b7c5e47fc4">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;    store_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00396"></a><span class="lineno">  396</span>&#160;  }</div><div class="line"><a name="l00397"></a><span class="lineno">  397</span>&#160;</div><div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00400"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a069840dd91f3a78de3ae9b10d5d8a9e5">  400</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a069840dd91f3a78de3ae9b10d5d8a9e5">load_with_pointer_offset</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#afc5b8faf449e4bd922e017b7c5e47fc4">Fragment</a> <span class="keyword">const</span> &amp;frag, <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a1d327b8913b60b3604924d697ebbaf6a">Index</a> pointer_offset) {</div><div class="line"><a name="l00401"></a><span class="lineno">  401</span>&#160;</div><div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;    <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">AccessType</a> *frag_ptr = <span class="keyword">reinterpret_cast&lt;</span><a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">AccessType</a> *<span class="keyword">&gt;</span>(&amp;frag);</div><div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div><div class="line"><a name="l00404"></a><span class="lineno">  404</span>&#160;    assert(0); <span class="comment">// TODO</span></div><div class="line"><a name="l00405"></a><span class="lineno">  405</span>&#160;  }</div><div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;</div><div class="line"><a name="l00408"></a><span class="lineno">  408</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00409"></a><span class="lineno"><a class="line" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ad7e957c4c7d02063f8b4c5eaeac92995">  409</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ad7e957c4c7d02063f8b4c5eaeac92995">load</a>(<a class="code" href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#afc5b8faf449e4bd922e017b7c5e47fc4">Fragment</a> <span class="keyword">const</span> &amp;frag) {</div><div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;    load_with_pointer_offset(frag, 0);</div><div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;  }</div><div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;};</div><div class="line"><a name="l00413"></a><span class="lineno">  413</span>&#160;</div><div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;</div><div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00417"></a><span class="lineno">  417</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00418"></a><span class="lineno">  418</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;</div><div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a069840dd91f3a78de3ae9b10d5d8a9e5"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a069840dd91f3a78de3ae9b10d5d8a9e5">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:400</div></div>
<div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_afbdcc5ca5b91f11f29046667b0bfde7b"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#afbdcc5ca5b91f11f29046667b0bfde7b">cutlass::MatrixCoord::column</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; column() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:85</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a3c929d73e2d8f8fba358fab7eaf3b91b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a3c929d73e2d8f8fba358fab7eaf3b91b">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Index</a></div><div class="ttdeci">typename TensorRef::Index Index</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:70</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="tensor__op__policy_8h_html"><div class="ttname"><a href="tensor__op__policy_8h.html">tensor_op_policy.h</a></div><div class="ttdoc">Defines basic structures needed for implementing the warp-scoped phase of the epilogue. These quantities assume a &amp;#39;column-major&amp;#39; arrangement of TensorOp instructions, of which a row-oriented slice is visible per iteration. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a7e2587da22e6d5273fcd33e43a76ff97"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a7e2587da22e6d5273fcd33e43a76ff97">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment const &amp;frag)</div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:232</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_ad2c00ec8815e91cf06491832a0ab7b9b"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ad2c00ec8815e91cf06491832a0ab7b9b">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:365</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_acca8fbc04b64161456135dae7f44d40c"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#acca8fbc04b64161456135dae7f44d40c">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::LongIndex</a></div><div class="ttdeci">typename TensorRef::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:71</div></div>
<div class="ttc" id="structcutlass_1_1half__t_html"><div class="ttname"><a href="structcutlass_1_1half__t.html">cutlass::half_t</a></div><div class="ttdoc">IEEE half-precision floating-point type. </div><div class="ttdef"><b>Definition:</b> half.h:126</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html_a0580610f28427e376b24b71f67602d03"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html#a0580610f28427e376b24b71f67602d03">cutlass::MatrixCoord::row</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; row() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> matrix_coord.h:77</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a34b485a73b0d06376d25c2a6e1049579"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a34b485a73b0d06376d25c2a6e1049579">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::AccessType</a></div><div class="ttdeci">typename Policy::AccessType AccessType</div><div class="ttdoc">Array type for aligned memory accesses. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:265</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_ab9627099133cc3fad152ea9984458147"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ab9627099133cc3fad152ea9984458147">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::add_tile_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp &amp; add_tile_offset(TensorCoord const &amp;tile_offset)</div><div class="ttdoc">advances in units of whole tiles along the logical coordinate space of the tensor ...</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:158</div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a5d55a2353bb364861072abade9fb34a5"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a5d55a2353bb364861072abade9fb34a5">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:63</div></div>
<div class="ttc" id="cutlass_8h_html_a4b1c9f25ab6eaa25e1f2258dd63e6ce4"><div class="ttname"><a href="cutlass_8h.html#a4b1c9f25ab6eaa25e1f2258dd63e6ce4">CUTLASS_PRAGMA_UNROLL</a></div><div class="ttdeci">#define CUTLASS_PRAGMA_UNROLL</div><div class="ttdef"><b>Definition:</b> cutlass.h:110</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_ac043e1542408af1268bef2b8a09ed7d9"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ac043e1542408af1268bef2b8a09ed7d9">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp &amp; operator+=(TensorCoord const &amp;tile_offset)</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:358</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_ad7e957c4c7d02063f8b4c5eaeac92995"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#ad7e957c4c7d02063f8b4c5eaeac92995">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::load</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load(Fragment const &amp;frag)</div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:409</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a10e475f4cadd7ec7371c5b74742a6081"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a10e475f4cadd7ec7371c5b74742a6081">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp &amp; operator+=(TensorCoord const &amp;tile_offset)</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:169</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_afc5b8faf449e4bd922e017b7c5e47fc4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#afc5b8faf449e4bd922e017b7c5e47fc4">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">typename Policy::Fragment Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:268</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_aa5aec99eff41c3c17177c792bbecb1ca"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#aa5aec99eff41c3c17177c792bbecb1ca">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp &amp; add_pointer_offset(Index pointer_offset)</div><div class="ttdoc">Adds a pointer offset. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:151</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp.html">cutlass::epilogue::warp::TileIteratorVoltaTensorOp</a></div><div class="ttdoc">Template for reading and writing tiles of accumulators to shared memory. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:52</div></div>
<div class="ttc" id="platform_8h_html_ab979d9d4b4923f7c54d6caa6e1a61936"><div class="ttname"><a href="platform_8h.html#ab979d9d4b4923f7c54d6caa6e1a61936">nullptr</a></div><div class="ttdeci">#define nullptr</div><div class="ttdoc">nullptr </div><div class="ttdef"><b>Definition:</b> platform.h:144</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; Element, Layout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a434b6974573b635cb4d8e3dc4d3dea74"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a434b6974573b635cb4d8e3dc4d3dea74">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::TileIteratorVoltaTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp()</div><div class="ttdoc">Default constructor. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:309</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a9c9ffc57742ab63f9bd943a33746461d"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a9c9ffc57742ab63f9bd943a33746461d">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::store</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:203</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a13ef705330675c5360e4430a9a002c07"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a13ef705330675c5360e4430a9a002c07">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::add_tile_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp &amp; add_tile_offset(TensorCoord const &amp;tile_offset)</div><div class="ttdoc">advances in units of whole tiles along the logical coordinate space of the tensor ...</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:347</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a5e3d6ed3bfdea5c21d74ff1d73570ced"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a5e3d6ed3bfdea5c21d74ff1d73570ced">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::store_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_DEVICE void store_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:176</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a30e13f9b73d66049f8233998f29823c4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a30e13f9b73d66049f8233998f29823c4">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">typename Policy::AccumulatorTile AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:271</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="structcutlass_1_1gemm_1_1GemmShape_html"><div class="ttname"><a href="structcutlass_1_1gemm_1_1GemmShape.html">cutlass::gemm::GemmShape</a></div><div class="ttdoc">Shape of a matrix multiply-add operation. </div><div class="ttdef"><b>Definition:</b> include/cutlass/gemm/gemm.h:57</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a5f4dc6e511ec485c9b03b6fe3d5cea64"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a5f4dc6e511ec485c9b03b6fe3d5cea64">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Element</a></div><div class="ttdeci">float Element</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:248</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a2a80643f2c5c65154827bec32489ea78"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a2a80643f2c5c65154827bec32489ea78">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::WarpShape</a></div><div class="ttdeci">WarpShape_ WarpShape</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:246</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a54fddd7011b6d199f69ff96dd1bbe806"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a54fddd7011b6d199f69ff96dd1bbe806">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::AccessType</a></div><div class="ttdeci">typename Policy::AccessType AccessType</div><div class="ttdoc">Array type for aligned memory accesses. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:82</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a5d111ad4fdbb7dfde82edb39b8d90cbc"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a5d111ad4fdbb7dfde82edb39b8d90cbc">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::LongIndex</a></div><div class="ttdeci">typename TensorRef::LongIndex LongIndex</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:254</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a11ec4b07a2132e647ca2ebe5112ce5ec"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a11ec4b07a2132e647ca2ebe5112ce5ec">cutlass::TensorRef::Index</a></div><div class="ttdeci">typename Layout::Index Index</div><div class="ttdoc">Index type. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:165</div></div>
<div class="ttc" id="classcutlass_1_1layout_1_1RowMajor_html"><div class="ttname"><a href="classcutlass_1_1layout_1_1RowMajor.html">cutlass::layout::RowMajor</a></div><div class="ttdoc">Mapping function for row-major matrices. </div><div class="ttdef"><b>Definition:</b> layout/matrix.h:50</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a7bf6d1c658c57c0112722b638c17cc58"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a7bf6d1c658c57c0112722b638c17cc58">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::TileIteratorVoltaTensorOp</a></div><div class="ttdeci">CUTLASS_DEVICE TileIteratorVoltaTensorOp(TensorRef const &amp;ref, unsigned lane_id)</div><div class="ttdoc">Constructor from TensorRef. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:130</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a77d5d1e5e8af463d1ec30bfee41f1161"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a77d5d1e5e8af463d1ec30bfee41f1161">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::Fragment</a></div><div class="ttdeci">typename Policy::Fragment Fragment</div><div class="ttdoc">This is the fragment size produced by one access of the iterator. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:85</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
<div class="ttc" id="pitch__linear_8h_html"><div class="ttname"><a href="pitch__linear_8h.html">pitch_linear.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes for pitch-linear memory. </div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a1d327b8913b60b3604924d697ebbaf6a"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a1d327b8913b60b3604924d697ebbaf6a">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::Index</a></div><div class="ttdeci">typename TensorRef::Index Index</div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:253</div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1VoltaTensorOpPolicy.html">cutlass::epilogue::warp::VoltaTensorOpPolicy</a></div><div class="ttdoc">Policy details related to the epilogue. </div><div class="ttdef"><b>Definition:</b> volta_tensor_op_policy.h:52</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a0a8719a2339a6eda6dc1f2f70fae8aea"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a0a8719a2339a6eda6dc1f2f70fae8aea">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::store</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void store(Fragment const &amp;frag)</div><div class="ttdoc">Store. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:394</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_ac192d6b2cd73d09ef1bd275fb58666e0"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#ac192d6b2cd73d09ef1bd275fb58666e0">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::load_with_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE void load_with_pointer_offset(Fragment const &amp;frag, Index pointer_offset)</div><div class="ttdoc">Load. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:209</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a565c2563388775804227ccf9c96c4dd4"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a565c2563388775804227ccf9c96c4dd4">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::add_pointer_offset</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp &amp; add_pointer_offset(Index pointer_offset)</div><div class="ttdoc">Adds a pointer offset. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:340</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644_html_a20f1b1e2a9bea03484cb709670ac4308"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmS2fe0c60b727c738c622c18fc3dd76644.html#a20f1b1e2a9bea03484cb709670ac4308">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, float, layout::RowMajor &gt;::TileIteratorVoltaTensorOp</a></div><div class="ttdeci">CUTLASS_DEVICE TileIteratorVoltaTensorOp(TensorRef const &amp;ref, unsigned lane_id)</div><div class="ttdoc">Constructor from TensorRef. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:313</div></div>
<div class="ttc" id="structcutlass_1_1MatrixCoord_html"><div class="ttname"><a href="structcutlass_1_1MatrixCoord.html">cutlass::MatrixCoord</a></div><div class="ttdef"><b>Definition:</b> matrix_coord.h:39</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_a8a79ba0606d52670402e37ee2e8d8a19"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#a8a79ba0606d52670402e37ee2e8d8a19">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::AccumulatorTile</a></div><div class="ttdeci">typename Policy::AccumulatorTile AccumulatorTile</div><div class="ttdoc">This is the complete warp-level accumulator tile. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:88</div></div>
<div class="ttc" id="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8_html_adf36e421cb4b5b01b465f0417bc931b2"><div class="ttname"><a href="classcutlass_1_1epilogue_1_1warp_1_1TileIteratorVoltaTensorOp_3_01WarpShape___00_01gemm_1_1GemmSa0ceeeddc22575876eb977da7f5416a8.html#adf36e421cb4b5b01b465f0417bc931b2">cutlass::epilogue::warp::TileIteratorVoltaTensorOp&lt; WarpShape_, gemm::GemmShape&lt; 32, 32, 4 &gt;, half_t, layout::RowMajor &gt;::TileIteratorVoltaTensorOp</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TileIteratorVoltaTensorOp()</div><div class="ttdoc">Default constructor. </div><div class="ttdef"><b>Definition:</b> tile_iterator_volta_tensor_op.h:126</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_adeada5e33b231f125a4aaeaf963bd3a3"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#adeada5e33b231f125a4aaeaf963bd3a3">cutlass::TensorRef::LongIndex</a></div><div class="ttdeci">typename Layout::LongIndex LongIndex</div><div class="ttdoc">Long index used for pointer offsets. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:168</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
