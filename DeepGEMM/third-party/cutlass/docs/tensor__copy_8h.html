<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_copy.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li><li class="navelem"><a class="el" href="dir_01de8928c960cafb028e5f164701e1de.html">reference</a></li><li class="navelem"><a class="el" href="dir_b790a865367d69962c5919afdba4a959.html">host</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#namespaces">Namespaces</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">tensor_copy.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">
<div class="textblock"><code>#include &lt;utility&gt;</code><br />
<code>#include &quot;<a class="el" href="cutlass_8h_source.html">cutlass/cutlass.h</a>&quot;</code><br />
<code>#include &quot;<a class="el" href="host_2tensor__foreach_8h_source.html">tensor_foreach.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for tensor_copy.h:</div>
<div class="dyncontent">
<div class="center"><img src="tensor__copy_8h__incl.png" border="0" usemap="#tensor__copy_8h" alt=""/></div>
<map name="tensor__copy_8h" id="tensor__copy_8h">
</map>
</div>
</div>
<p><a href="tensor__copy_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html">cutlass::reference::host::detail::TrivialConvert&lt; DstElement, SrcElement &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Helper to convert between types.  <a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Helper to conditionally copy between tensor views.  <a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="namespaces"></a>
Namespaces</h2></td></tr>
<tr class="memitem:namespacecutlass"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass.html">cutlass</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1reference"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference.html">cutlass::reference</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1reference_1_1host"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html">cutlass::reference::host</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:namespacecutlass_1_1reference_1_1host_1_1detail"><td class="memItemLeft" align="right" valign="top"> &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host_1_1detail.html">cutlass::reference::host::detail</a></td></tr>
<tr class="memdesc:namespacecutlass_1_1reference_1_1host_1_1detail"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines several helpers. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ab32bea7b552a408f93fc2f153b44fcb8"><td class="memTemplParams" colspan="2">template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </td></tr>
<tr class="memitem:ab32bea7b552a408f93fc2f153b44fcb8"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">cutlass::reference::host::TensorCopy</a> (TensorView&lt; DstElement, DstLayout &gt; dst, TensorView&lt; SrcElement, SrcLayout &gt; src, F const &amp;transform)</td></tr>
<tr class="memdesc:ab32bea7b552a408f93fc2f153b44fcb8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copies elements from one tensor view into another, satisfying bounds of each tensor.  <a href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">More...</a><br /></td></tr>
<tr class="separator:ab32bea7b552a408f93fc2f153b44fcb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a32903a34034cfe040157a5cd48c325ce"><td class="memTemplParams" colspan="2">template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </td></tr>
<tr class="memitem:a32903a34034cfe040157a5cd48c325ce"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#a32903a34034cfe040157a5cd48c325ce">cutlass::reference::host::TensorCopy</a> (TensorView&lt; DstElement, DstLayout &gt; dst, TensorRef&lt; SrcElement, SrcLayout &gt; src, F const &amp;transform)</td></tr>
<tr class="separator:a32903a34034cfe040157a5cd48c325ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a723c4026b4a73a6050aa203aee95de84"><td class="memTemplParams" colspan="2">template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </td></tr>
<tr class="memitem:a723c4026b4a73a6050aa203aee95de84"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#a723c4026b4a73a6050aa203aee95de84">cutlass::reference::host::TensorCopy</a> (TensorRef&lt; DstElement, DstLayout &gt; dst, TensorView&lt; SrcElement, SrcLayout &gt; src, F const &amp;transform)</td></tr>
<tr class="separator:a723c4026b4a73a6050aa203aee95de84"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ffea013419e1ac514797633dd46d6a6"><td class="memTemplParams" colspan="2">template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout &gt; </td></tr>
<tr class="memitem:a1ffea013419e1ac514797633dd46d6a6"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#a1ffea013419e1ac514797633dd46d6a6">cutlass::reference::host::TensorCopy</a> (TensorView&lt; DstElement, DstLayout &gt; dst, TensorView&lt; SrcElement, SrcLayout &gt; src)</td></tr>
<tr class="separator:a1ffea013419e1ac514797633dd46d6a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aae15ab711d7be5074953df3fa62bae0d"><td class="memTemplParams" colspan="2">template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </td></tr>
<tr class="memitem:aae15ab711d7be5074953df3fa62bae0d"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#aae15ab711d7be5074953df3fa62bae0d">cutlass::reference::host::TensorCopy</a> (TensorView&lt; DstElement, DstLayout &gt; dst, TensorRef&lt; SrcElement, SrcLayout &gt; src)</td></tr>
<tr class="separator:aae15ab711d7be5074953df3fa62bae0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a513f4fef76bc1a6b34b39f0d6bd7c48a"><td class="memTemplParams" colspan="2">template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout &gt; </td></tr>
<tr class="memitem:a513f4fef76bc1a6b34b39f0d6bd7c48a"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="namespacecutlass_1_1reference_1_1host.html#a513f4fef76bc1a6b34b39f0d6bd7c48a">cutlass::reference::host::TensorCopy</a> (TensorRef&lt; DstElement, DstLayout &gt; dst, TensorView&lt; SrcElement, SrcLayout &gt; src)</td></tr>
<tr class="separator:a513f4fef76bc1a6b34b39f0d6bd7c48a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
