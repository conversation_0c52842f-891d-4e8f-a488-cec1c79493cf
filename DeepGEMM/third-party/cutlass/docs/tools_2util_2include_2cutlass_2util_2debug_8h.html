<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: debug.h File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#define-members">Macros</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">tools/util/include/cutlass/util/debug.h File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Contains code for debugging cutlass code.  
<a href="#details">More...</a></p>
<div class="textblock"><code>#include &quot;<a class="el" href="device__dump_8h_source.html">device_dump.h</a>&quot;</code><br />
</div><div class="textblock"><div class="dynheader">
Include dependency graph for tools/util/include/cutlass/util/debug.h:</div>
<div class="dyncontent">
<div class="center"><img src="tools_2util_2include_2cutlass_2util_2debug_8h__incl.png" border="0" usemap="#debug_8h" alt=""/></div>
<map name="debug_8h" id="debug_8h">
</map>
</div>
</div>
<p><a href="tools_2util_2include_2cutlass_2util_2debug_8h_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structDebugType.html">DebugType&lt; T &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structDebugValue.html">DebugValue&lt; Value &gt;</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="define-members"></a>
Macros</h2></td></tr>
<tr class="memitem:a27e3466bcf1ec7fda4f6f95aa0a51177"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a27e3466bcf1ec7fda4f6f95aa0a51177">CUDA_LOG</a>(format, ...)&#160;&#160;&#160;printf(format, __VA_ARGS__)</td></tr>
<tr class="separator:a27e3466bcf1ec7fda4f6f95aa0a51177"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8d6986db819719ada8b29d53dfc104a6"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a8d6986db819719ada8b29d53dfc104a6">CUDA_LOG_DEBUG</a>(format, ...)</td></tr>
<tr class="separator:a8d6986db819719ada8b29d53dfc104a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aed8337b88d71895f95f8980ef0b3a50b"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed8337b88d71895f95f8980ef0b3a50b">CUDA_PERROR</a>(e)&#160;&#160;&#160;<a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">cuda_perror_impl</a>((cudaError_t)(e), __FILE__, __LINE__)</td></tr>
<tr class="memdesc:aed8337b88d71895f95f8980ef0b3a50b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Perror macro.  <a href="#aed8337b88d71895f95f8980ef0b3a50b">More...</a><br /></td></tr>
<tr class="separator:aed8337b88d71895f95f8980ef0b3a50b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a002632ff687c83cff0484476be401f05"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a002632ff687c83cff0484476be401f05">CUDA_PERROR_EXIT</a>(e)</td></tr>
<tr class="memdesc:a002632ff687c83cff0484476be401f05"><td class="mdescLeft">&#160;</td><td class="mdescRight">Perror macro with exit.  <a href="#a002632ff687c83cff0484476be401f05">More...</a><br /></td></tr>
<tr class="separator:a002632ff687c83cff0484476be401f05"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36436f5408940a47ac5cdfc9b31648db"><td class="memItemLeft" align="right" valign="top">#define&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#a36436f5408940a47ac5cdfc9b31648db">CUDA_PERROR_DEBUG</a>(e)&#160;&#160;&#160;(e)</td></tr>
<tr class="memdesc:a36436f5408940a47ac5cdfc9b31648db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Perror macro only if DEBUG is defined.  <a href="#a36436f5408940a47ac5cdfc9b31648db">More...</a><br /></td></tr>
<tr class="separator:a36436f5408940a47ac5cdfc9b31648db"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:aed67644b6ffaa6d88a2efa09fcaafdce"><td class="memItemLeft" align="right" valign="top">__host__ CUTLASS_DEVICE cudaError_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">cuda_perror_impl</a> (cudaError_t error, const char *filename, int line)</td></tr>
<tr class="memdesc:aed67644b6ffaa6d88a2efa09fcaafdce"><td class="mdescLeft">&#160;</td><td class="mdescRight">The corresponding error message is printed to <code>stderr</code> (or <code>stdout</code> in device code) along with the supplied source context.  <a href="#aed67644b6ffaa6d88a2efa09fcaafdce">More...</a><br /></td></tr>
<tr class="separator:aed67644b6ffaa6d88a2efa09fcaafdce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab7e23b523490567225b20e2c72649f20"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ab7e23b523490567225b20e2c72649f20"><td class="memTemplItemLeft" align="right" valign="top">void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#ab7e23b523490567225b20e2c72649f20">DebugTypeFunc</a> (T const &amp;t)</td></tr>
<tr class="separator:ab7e23b523490567225b20e2c72649f20"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Macro Definition Documentation</h2>
<a class="anchor" id="a27e3466bcf1ec7fda4f6f95aa0a51177"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUDA_LOG</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">format, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>...</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td>&#160;&#160;&#160;printf(format, __VA_ARGS__)</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Formats and prints the given message to stdout </p>

</div>
</div>
<a class="anchor" id="a8d6986db819719ada8b29d53dfc104a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUDA_LOG_DEBUG</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">format, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">&#160;</td>
          <td class="paramname"><em>...</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Formats and prints the given message to stdout only if DEBUG is defined </p>

</div>
</div>
<a class="anchor" id="aed8337b88d71895f95f8980ef0b3a50b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUDA_PERROR</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">e</td><td>)</td>
          <td>&#160;&#160;&#160;<a class="el" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">cuda_perror_impl</a>((cudaError_t)(e), __FILE__, __LINE__)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a36436f5408940a47ac5cdfc9b31648db"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUDA_PERROR_DEBUG</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">e</td><td>)</td>
          <td>&#160;&#160;&#160;(e)</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a002632ff687c83cff0484476be401f05"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define CUDA_PERROR_EXIT</td>
          <td>(</td>
          <td class="paramtype">&#160;</td>
          <td class="paramname">e</td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">
<b>Value:</b><div class="fragment"><div class="line"><span class="keywordflow">if</span> (<a class="code" href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">cuda_perror_impl</a>((cudaError_t)(e), __FILE__, __LINE__)) { \</div><div class="line">    exit(1);                                                    \</div><div class="line">  }</div><div class="ttc" id="tools_2util_2include_2cutlass_2util_2debug_8h_html_aed67644b6ffaa6d88a2efa09fcaafdce"><div class="ttname"><a href="tools_2util_2include_2cutlass_2util_2debug_8h.html#aed67644b6ffaa6d88a2efa09fcaafdce">cuda_perror_impl</a></div><div class="ttdeci">__host__ CUTLASS_DEVICE cudaError_t cuda_perror_impl(cudaError_t error, const char *filename, int line)</div><div class="ttdoc">The corresponding error message is printed to stderr (or stdout in device code) along with the suppli...</div><div class="ttdef"><b>Definition:</b> tools/util/include/cutlass/util/debug.h:76</div></div>
</div><!-- fragment -->
</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="aed67644b6ffaa6d88a2efa09fcaafdce"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">__host__ CUTLASS_DEVICE cudaError_t cuda_perror_impl </td>
          <td>(</td>
          <td class="paramtype">cudaError_t&#160;</td>
          <td class="paramname"><em>error</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&#160;</td>
          <td class="paramname"><em>filename</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>line</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">
<dl class="section return"><dt>Returns</dt><dd>The CUDA error. </dd></dl>

</div>
</div>
<a class="anchor" id="ab7e23b523490567225b20e2c72649f20"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">void DebugTypeFunc </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
