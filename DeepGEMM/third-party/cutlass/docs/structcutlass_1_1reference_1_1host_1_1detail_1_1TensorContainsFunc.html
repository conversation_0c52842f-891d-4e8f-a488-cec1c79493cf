<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::host::detail::TensorContainsFunc&lt; Element, Layout &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host.html">host</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">TensorContainsFunc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::host::detail::TensorContainsFunc&lt; Element, Layout &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>&lt; Layout function  
</p>

<p><code>#include &lt;<a class="el" href="host_2tensor__compare_8h_source.html">tensor_compare.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::host::detail::TensorContainsFunc&lt; Element, Layout &gt;:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc_3_01Element_00_01Layout_01_4_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc_3_01Element_00_01Layout_01_4_coll__map" id="cutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc_3_01Element_00_01Layout_01_4_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a94851c3df358846eb64df3dc5c17effe"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#a94851c3df358846eb64df3dc5c17effe">TensorContainsFunc</a> ()</td></tr>
<tr class="memdesc:a94851c3df358846eb64df3dc5c17effe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ctor.  <a href="#a94851c3df358846eb64df3dc5c17effe">More...</a><br /></td></tr>
<tr class="separator:a94851c3df358846eb64df3dc5c17effe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a636bd16f4e88020b34ebdfce7379bf21"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#a636bd16f4e88020b34ebdfce7379bf21">TensorContainsFunc</a> (<a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; Element, Layout &gt; const &amp;view_, Element value_)</td></tr>
<tr class="memdesc:a636bd16f4e88020b34ebdfce7379bf21"><td class="mdescLeft">&#160;</td><td class="mdescRight">Ctor.  <a href="#a636bd16f4e88020b34ebdfce7379bf21">More...</a><br /></td></tr>
<tr class="separator:a636bd16f4e88020b34ebdfce7379bf21"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a6e4363ba937eebda8642074662fb985b"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#a6e4363ba937eebda8642074662fb985b">operator()</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Layout::kRank &gt; const &amp;coord)</td></tr>
<tr class="memdesc:a6e4363ba937eebda8642074662fb985b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Visits a coordinate.  <a href="#a6e4363ba937eebda8642074662fb985b">More...</a><br /></td></tr>
<tr class="separator:a6e4363ba937eebda8642074662fb985b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a7766766e9565c6ece73c89baa9ae1d70"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#a7766766e9565c6ece73c89baa9ae1d70">operator bool</a> () const </td></tr>
<tr class="memdesc:a7766766e9565c6ece73c89baa9ae1d70"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if equal.  <a href="#a7766766e9565c6ece73c89baa9ae1d70">More...</a><br /></td></tr>
<tr class="separator:a7766766e9565c6ece73c89baa9ae1d70"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:aa2424651ff004007fbb4268b727573d1"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; Element, Layout &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#aa2424651ff004007fbb4268b727573d1">view</a></td></tr>
<tr class="separator:aa2424651ff004007fbb4268b727573d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80bf16c749e4982762864ef9ef88969a"><td class="memItemLeft" align="right" valign="top">Element&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#a80bf16c749e4982762864ef9ef88969a">value</a></td></tr>
<tr class="separator:a80bf16c749e4982762864ef9ef88969a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a36f817e5b6e993ac3c9aaf78186a1ffb"><td class="memItemLeft" align="right" valign="top">bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#a36f817e5b6e993ac3c9aaf78186a1ffb">contains</a></td></tr>
<tr class="separator:a36f817e5b6e993ac3c9aaf78186a1ffb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a77a8114df44c697e262f2b6afac6255e"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Layout::kRank &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html#a77a8114df44c697e262f2b6afac6255e">location</a></td></tr>
<tr class="separator:a77a8114df44c697e262f2b6afac6255e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a94851c3df358846eb64df3dc5c17effe"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">TensorContainsFunc</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a636bd16f4e88020b34ebdfce7379bf21"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">TensorContainsFunc</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; Element, Layout &gt; const &amp;&#160;</td>
          <td class="paramname"><em>view_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Element&#160;</td>
          <td class="paramname"><em>value_</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a7766766e9565c6ece73c89baa9ae1d70"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::operator bool </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a6e4363ba937eebda8642074662fb985b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; Layout::kRank &gt; const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a36f817e5b6e993ac3c9aaf78186a1ffb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">bool <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::contains</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a77a8114df44c697e262f2b6afac6255e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt;Layout::kRank&gt; <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::location</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a80bf16c749e4982762864ef9ef88969a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">Element <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::value</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa2424651ff004007fbb4268b727573d1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element, typename Layout&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt;Element, Layout&gt; <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorContainsFunc.html">cutlass::reference::host::detail::TensorContainsFunc</a>&lt; Element, Layout &gt;::view</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="host_2tensor__compare_8h_source.html">host/tensor_compare.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
