<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::PitchLinearWarpRakedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">PitchLinearWarpRakedThreadMap</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html">Detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::PitchLinearWarpRakedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Internal details made public to facilitate introspection Iterations along each dimension (concept: PitchLinearShape)  
</p>

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:ae5f2c732f0ae6ecedb89b86de30aacf1"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#ae5f2c732f0ae6ecedb89b86de30aacf1">WarpThreadArrangement</a> = WarpThreadArrangement_</td></tr>
<tr class="memdesc:ae5f2c732f0ae6ecedb89b86de30aacf1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fixed arrangement of threads within a warp (units of threads).  <a href="#ae5f2c732f0ae6ecedb89b86de30aacf1">More...</a><br /></td></tr>
<tr class="separator:ae5f2c732f0ae6ecedb89b86de30aacf1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a0b8f5e71ded5e5334268c989ba32e3b5"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a0b8f5e71ded5e5334268c989ba32e3b5">ShapeInAccesses</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Shape::kContiguous/<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750">kElementsPerAccess</a>, Shape::kStrided &gt;</td></tr>
<tr class="memdesc:a0b8f5e71ded5e5334268c989ba32e3b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the 'shape' of the overall tile in units of vectors.  <a href="#a0b8f5e71ded5e5334268c989ba32e3b5">More...</a><br /></td></tr>
<tr class="separator:a0b8f5e71ded5e5334268c989ba32e3b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a396329f54c14eb236b01e3b3d6d1951d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a396329f54c14eb236b01e3b3d6d1951d">WarpAccessIterations</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">ShapeInAccesses::kContiguous</a>/WarpThreadArrangement::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">ShapeInAccesses::kStrided</a>/WarpThreadArrangement::kStrided &gt;</td></tr>
<tr class="separator:a396329f54c14eb236b01e3b3d6d1951d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab96f66e4794d80ca8df425be845e782f"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#ab96f66e4794d80ca8df425be845e782f">WarpArrangement</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a05bcdb4e8fb32bfe30ac862ffc5de279">kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a74a1c31d82466657f6d2eb4126ed212f">kWarpsStrided</a> &gt;</td></tr>
<tr class="memdesc:ab96f66e4794d80ca8df425be845e782f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arrangement of warps within a threadblock-scoped tile.  <a href="#ab96f66e4794d80ca8df425be845e782f">More...</a><br /></td></tr>
<tr class="separator:ab96f66e4794d80ca8df425be845e782f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a4a9356476175000839fe4dd4d642311a"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a4a9356476175000839fe4dd4d642311a">kWarpSize</a> = WarpThreadArrangement::kCount</td></tr>
<tr class="memdesc:a4a9356476175000839fe4dd4d642311a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads per warp.  <a href="#a4a9356476175000839fe4dd4d642311a">More...</a><br /></td></tr>
<tr class="separator:a4a9356476175000839fe4dd4d642311a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80d262dcae35245106c627941bf2e2c5"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a80d262dcae35245106c627941bf2e2c5">kWarpCount</a> = <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ab0d2e70502e80e8f85ef7b83cade093c">kThreads</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a4a9356476175000839fe4dd4d642311a">kWarpSize</a></td></tr>
<tr class="memdesc:a80d262dcae35245106c627941bf2e2c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of participating warps.  <a href="#a80d262dcae35245106c627941bf2e2c5">More...</a><br /></td></tr>
<tr class="separator:a80d262dcae35245106c627941bf2e2c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a74a1c31d82466657f6d2eb4126ed212f"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a74a1c31d82466657f6d2eb4126ed212f">kWarpsStrided</a></td></tr>
<tr class="separator:a74a1c31d82466657f6d2eb4126ed212f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a05bcdb4e8fb32bfe30ac862ffc5de279"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a05bcdb4e8fb32bfe30ac862ffc5de279">kWarpsContiguous</a></td></tr>
<tr class="separator:a05bcdb4e8fb32bfe30ac862ffc5de279"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a0b8f5e71ded5e5334268c989ba32e3b5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a0b8f5e71ded5e5334268c989ba32e3b5">Detail::ShapeInAccesses</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Shape::kContiguous / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750">kElementsPerAccess</a>, Shape::kStrided &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a396329f54c14eb236b01e3b3d6d1951d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a396329f54c14eb236b01e3b3d6d1951d">Detail::WarpAccessIterations</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">ShapeInAccesses::kContiguous</a> / WarpThreadArrangement::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">ShapeInAccesses::kStrided</a> / WarpThreadArrangement::kStrided &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab96f66e4794d80ca8df425be845e782f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#ab96f66e4794d80ca8df425be845e782f">Detail::WarpArrangement</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a05bcdb4e8fb32bfe30ac862ffc5de279">kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a74a1c31d82466657f6d2eb4126ed212f">kWarpsStrided</a> &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae5f2c732f0ae6ecedb89b86de30aacf1"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#ae5f2c732f0ae6ecedb89b86de30aacf1">Detail::WarpThreadArrangement</a> =  WarpThreadArrangement_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a80d262dcae35245106c627941bf2e2c5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpCount = <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ab0d2e70502e80e8f85ef7b83cade093c">kThreads</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a4a9356476175000839fe4dd4d642311a">kWarpSize</a></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a05bcdb4e8fb32bfe30ac862ffc5de279"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpsContiguous</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div><div class="line">        (<a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a80d262dcae35245106c627941bf2e2c5">kWarpCount</a> &gt; <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">WarpAccessIterations::kStrided</a></div><div class="line">             ? <a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a80d262dcae35245106c627941bf2e2c5">kWarpCount</a> / <a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a74a1c31d82466657f6d2eb4126ed212f">kWarpsStrided</a></div><div class="line">             : 1)</div></div><!-- fragment -->
</div>
</div>
<a class="anchor" id="a4a9356476175000839fe4dd4d642311a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpSize = WarpThreadArrangement::kCount</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a74a1c31d82466657f6d2eb4126ed212f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpsStrided</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">=</div><div class="line">        (<a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">WarpAccessIterations::kStrided</a> &gt;= <a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a80d262dcae35245106c627941bf2e2c5">kWarpCount</a></div><div class="line">             ? <a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a80d262dcae35245106c627941bf2e2c5">kWarpCount</a></div><div class="line">             : <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">WarpAccessIterations::kStrided</a>)</div></div><!-- fragment -->
</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
