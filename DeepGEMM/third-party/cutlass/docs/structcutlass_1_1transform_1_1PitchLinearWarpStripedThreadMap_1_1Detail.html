<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::PitchLinearWarpStripedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">PitchLinearWarpStripedThreadMap</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html">Detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::PitchLinearWarpStripedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Internal details made public to facilitate introspection Iterations along each dimension (concept: PitchLinearShape)  
</p>

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a3c9d519f149afb61d0e083ead0e2a13f"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a3c9d519f149afb61d0e083ead0e2a13f">WarpThreadArrangement</a> = WarpThreadArrangement_</td></tr>
<tr class="memdesc:a3c9d519f149afb61d0e083ead0e2a13f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Fixed arrangement of threads within a warp (units of threads).  <a href="#a3c9d519f149afb61d0e083ead0e2a13f">More...</a><br /></td></tr>
<tr class="separator:a3c9d519f149afb61d0e083ead0e2a13f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae3b090d367aff722f358aba13da9974f"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#ae3b090d367aff722f358aba13da9974f">ShapeInAccesses</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Shape::kContiguous/<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858">kElementsPerAccess</a>, Shape::kStrided &gt;</td></tr>
<tr class="memdesc:ae3b090d367aff722f358aba13da9974f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the 'shape' of the overall tile in units of vectors.  <a href="#ae3b090d367aff722f358aba13da9974f">More...</a><br /></td></tr>
<tr class="separator:ae3b090d367aff722f358aba13da9974f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac7045a4a202cdf9e6b91e0d113f6c4ff"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#ac7045a4a202cdf9e6b91e0d113f6c4ff">WarpAccessIterations</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">ShapeInAccesses::kContiguous</a>/WarpThreadArrangement::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">ShapeInAccesses::kStrided</a>/WarpThreadArrangement::kStrided &gt;</td></tr>
<tr class="separator:ac7045a4a202cdf9e6b91e0d113f6c4ff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a46cc0b7ee74ea8607cd79b83709e0998"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a46cc0b7ee74ea8607cd79b83709e0998">WarpArrangement</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a363250282dfea2d4c3c42500b6fb90e9">kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a20bc67607c17bf1fc367a6b3a3bb6ad3">kWarpsStrided</a> &gt;</td></tr>
<tr class="memdesc:a46cc0b7ee74ea8607cd79b83709e0998"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arrangement of warps within a threadblock-scoped tile.  <a href="#a46cc0b7ee74ea8607cd79b83709e0998">More...</a><br /></td></tr>
<tr class="separator:a46cc0b7ee74ea8607cd79b83709e0998"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:adc91199cda80ad6d1b41681b319fc3d6"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#adc91199cda80ad6d1b41681b319fc3d6">kWarpSize</a> = WarpThreadArrangement::kCount</td></tr>
<tr class="memdesc:adc91199cda80ad6d1b41681b319fc3d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads per warp.  <a href="#adc91199cda80ad6d1b41681b319fc3d6">More...</a><br /></td></tr>
<tr class="separator:adc91199cda80ad6d1b41681b319fc3d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af6622be9e439ee44ee1f05b8ea696450"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#af6622be9e439ee44ee1f05b8ea696450">kWarpCount</a> = <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a58fa53118303ef62f8b7557616606f8f">kThreads</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#adc91199cda80ad6d1b41681b319fc3d6">kWarpSize</a></td></tr>
<tr class="memdesc:af6622be9e439ee44ee1f05b8ea696450"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of participating warps.  <a href="#af6622be9e439ee44ee1f05b8ea696450">More...</a><br /></td></tr>
<tr class="separator:af6622be9e439ee44ee1f05b8ea696450"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a20bc67607c17bf1fc367a6b3a3bb6ad3"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a20bc67607c17bf1fc367a6b3a3bb6ad3">kWarpsStrided</a></td></tr>
<tr class="separator:a20bc67607c17bf1fc367a6b3a3bb6ad3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a363250282dfea2d4c3c42500b6fb90e9"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a363250282dfea2d4c3c42500b6fb90e9">kWarpsContiguous</a></td></tr>
<tr class="separator:a363250282dfea2d4c3c42500b6fb90e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ae3b090d367aff722f358aba13da9974f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#ae3b090d367aff722f358aba13da9974f">Detail::ShapeInAccesses</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Shape::kContiguous / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a8d3b364145189790a0e5fa033d63f858">kElementsPerAccess</a>, Shape::kStrided &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac7045a4a202cdf9e6b91e0d113f6c4ff"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#ac7045a4a202cdf9e6b91e0d113f6c4ff">Detail::WarpAccessIterations</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">ShapeInAccesses::kContiguous</a> / WarpThreadArrangement::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">ShapeInAccesses::kStrided</a> / WarpThreadArrangement::kStrided &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a46cc0b7ee74ea8607cd79b83709e0998"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a46cc0b7ee74ea8607cd79b83709e0998">Detail::WarpArrangement</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a363250282dfea2d4c3c42500b6fb90e9">kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a20bc67607c17bf1fc367a6b3a3bb6ad3">kWarpsStrided</a> &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3c9d519f149afb61d0e083ead0e2a13f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a3c9d519f149afb61d0e083ead0e2a13f">Detail::WarpThreadArrangement</a> =  WarpThreadArrangement_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="af6622be9e439ee44ee1f05b8ea696450"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpCount = <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html#a58fa53118303ef62f8b7557616606f8f">kThreads</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#adc91199cda80ad6d1b41681b319fc3d6">kWarpSize</a></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a363250282dfea2d4c3c42500b6fb90e9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpsContiguous</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= </div><div class="line">      (<a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#af6622be9e439ee44ee1f05b8ea696450">kWarpCount</a> &gt; <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">WarpAccessIterations::kStrided</a> ? </div><div class="line">        <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">WarpAccessIterations::kContiguous</a> / <a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#a20bc67607c17bf1fc367a6b3a3bb6ad3">kWarpsStrided</a> : 1)</div></div><!-- fragment -->
</div>
</div>
<a class="anchor" id="adc91199cda80ad6d1b41681b319fc3d6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpSize = WarpThreadArrangement::kCount</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a20bc67607c17bf1fc367a6b3a3bb6ad3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap.html">cutlass::transform::PitchLinearWarpStripedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::Detail::kWarpsStrided</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<b>Initial value:</b><div class="fragment"><div class="line">= </div><div class="line">      (<a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">WarpAccessIterations::kStrided</a> &gt;= <a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#af6622be9e439ee44ee1f05b8ea696450">kWarpCount</a> </div><div class="line">        ? <a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#af6622be9e439ee44ee1f05b8ea696450">kWarpCount</a> : (<a class="code" href="structcutlass_1_1transform_1_1PitchLinearWarpStripedThreadMap_1_1Detail.html#af6622be9e439ee44ee1f05b8ea696450">kWarpCount</a> / <a class="code" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">WarpAccessIterations::kStrided</a>))</div></div><!-- fragment -->
</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
