<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::device::detail::TensorCopyDiagonalInFunc&lt; Element, Layout &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device.html">device</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">TensorCopyDiagonalInFunc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::device::detail::TensorCopyDiagonalInFunc&lt; Element, Layout &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Computes a random Gaussian distribution.  
 <a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#details">More...</a></p>

<p><code>#include &lt;<a class="el" href="device_2tensor__fill_8h_source.html">tensor_fill.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::device::detail::TensorCopyDiagonalInFunc&lt; Element, Layout &gt;:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_3_01Element_00_01Layout_01_4_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_3_01Element_00_01Layout_01_4_coll__map" id="cutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_3_01Element_00_01Layout_01_4_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html">Params</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameters structure.  <a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a8510e634ed1a482dc6b4baeaac881caa"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a> = <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a>&lt; Element, Layout &gt;</td></tr>
<tr class="memdesc:a8510e634ed1a482dc6b4baeaac881caa"><td class="mdescLeft">&#160;</td><td class="mdescRight">View type.  <a href="#a8510e634ed1a482dc6b4baeaac881caa">More...</a><br /></td></tr>
<tr class="separator:a8510e634ed1a482dc6b4baeaac881caa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7ff417d1b9a9fa9824b57bbc9716223"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">TensorView::Element</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#ae7ff417d1b9a9fa9824b57bbc9716223">T</a></td></tr>
<tr class="memdesc:ae7ff417d1b9a9fa9824b57bbc9716223"><td class="mdescLeft">&#160;</td><td class="mdescRight">Scalar type.  <a href="#ae7ff417d1b9a9fa9824b57bbc9716223">More...</a><br /></td></tr>
<tr class="separator:ae7ff417d1b9a9fa9824b57bbc9716223"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a80676e67d778e538981b0dc1fc9bd008"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorView::TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a80676e67d778e538981b0dc1fc9bd008">TensorCoord</a></td></tr>
<tr class="memdesc:a80676e67d778e538981b0dc1fc9bd008"><td class="mdescLeft">&#160;</td><td class="mdescRight">Coordinate in tensor's index space.  <a href="#a80676e67d778e538981b0dc1fc9bd008">More...</a><br /></td></tr>
<tr class="separator:a80676e67d778e538981b0dc1fc9bd008"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aeb63ecaca6cb9c523460736d187e7817"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#aeb63ecaca6cb9c523460736d187e7817">TensorCopyDiagonalInFunc</a> (<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html">Params</a> const &amp;<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a74b866ebefe84dd33f31977f189adebe">params</a>)</td></tr>
<tr class="memdesc:aeb63ecaca6cb9c523460736d187e7817"><td class="mdescLeft">&#160;</td><td class="mdescRight">Device-side initialization of RNG.  <a href="#aeb63ecaca6cb9c523460736d187e7817">More...</a><br /></td></tr>
<tr class="separator:aeb63ecaca6cb9c523460736d187e7817"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af700e9ac6ece02af0ce80fb8ef792084"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#af700e9ac6ece02af0ce80fb8ef792084">operator()</a> (<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a80676e67d778e538981b0dc1fc9bd008">TensorCoord</a> const &amp;coord)</td></tr>
<tr class="memdesc:af700e9ac6ece02af0ce80fb8ef792084"><td class="mdescLeft">&#160;</td><td class="mdescRight">Only update the diagonal element.  <a href="#af700e9ac6ece02af0ce80fb8ef792084">More...</a><br /></td></tr>
<tr class="separator:af700e9ac6ece02af0ce80fb8ef792084"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a74b866ebefe84dd33f31977f189adebe"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html">Params</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a74b866ebefe84dd33f31977f189adebe">params</a></td></tr>
<tr class="memdesc:a74b866ebefe84dd33f31977f189adebe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameters object.  <a href="#a74b866ebefe84dd33f31977f189adebe">More...</a><br /></td></tr>
<tr class="separator:a74b866ebefe84dd33f31977f189adebe"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename Element, typename Layout&gt;<br />
struct cutlass::reference::device::detail::TensorCopyDiagonalInFunc&lt; Element, Layout &gt;</h3>

<p>&lt; Layout function </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ae7ff417d1b9a9fa9824b57bbc9716223"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1TensorView.html#afe228764eb67b664fb5ca320c092903b">TensorView::Element</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#ae7ff417d1b9a9fa9824b57bbc9716223">T</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a80676e67d778e538981b0dc1fc9bd008"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="classcutlass_1_1TensorView.html#a893017197cb29d46773feea6cdbb25db">TensorView::TensorCoord</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a80676e67d778e538981b0dc1fc9bd008">TensorCoord</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8510e634ed1a482dc6b4baeaac881caa"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a> =  <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a>&lt;Element, Layout&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="aeb63ecaca6cb9c523460736d187e7817"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">TensorCopyDiagonalInFunc</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html">Params</a> const &amp;&#160;</td>
          <td class="paramname"><em>params</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="af700e9ac6ece02af0ce80fb8ef792084"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE void <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a80676e67d778e538981b0dc1fc9bd008">TensorCoord</a> const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a74b866ebefe84dd33f31977f189adebe"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html">Params</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::params</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="device_2tensor__fill_8h_source.html">device/tensor_fill.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
