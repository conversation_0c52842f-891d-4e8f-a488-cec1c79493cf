<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::device::detail::TensorCopyDiagonalInFunc&lt; Element, Layout &gt;::Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device.html">device</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">TensorCopyDiagonalInFunc</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html">Params</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::device::detail::TensorCopyDiagonalInFunc&lt; Element, Layout &gt;::Params Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Parameters structure.  
</p>

<p><code>#include &lt;<a class="el" href="device_2tensor__fill_8h_source.html">tensor_fill.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::device::detail::TensorCopyDiagonalInFunc&lt; Element, Layout &gt;::Params:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_3_01Element_00_01Layout_01_4_1_1Params_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_3_01Element_00_01Layout_01_4_1_1Params_coll__map" id="cutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_3_01Element_00_01Layout_01_4_1_1Params_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a24b17f2db455bfb0d86f6534c6850766"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html#a24b17f2db455bfb0d86f6534c6850766">Params</a> ()</td></tr>
<tr class="memdesc:a24b17f2db455bfb0d86f6534c6850766"><td class="mdescLeft">&#160;</td><td class="mdescRight">Default ctor.  <a href="#a24b17f2db455bfb0d86f6534c6850766">More...</a><br /></td></tr>
<tr class="separator:a24b17f2db455bfb0d86f6534c6850766"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a34d3d2fa3894cc57964ac1af16a8612a"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html#a34d3d2fa3894cc57964ac1af16a8612a">Params</a> (<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a> view_, Element const *ptr_)</td></tr>
<tr class="memdesc:a34d3d2fa3894cc57964ac1af16a8612a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Construction of Gaussian RNG functor.  <a href="#a34d3d2fa3894cc57964ac1af16a8612a">More...</a><br /></td></tr>
<tr class="separator:a34d3d2fa3894cc57964ac1af16a8612a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:aaaeac91c7344b4b29b290ffd095ef57f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html#aaaeac91c7344b4b29b290ffd095ef57f">view</a></td></tr>
<tr class="separator:aaaeac91c7344b4b29b290ffd095ef57f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a088d18e084a3bd3c60ef12069b70b03c"><td class="memItemLeft" align="right" valign="top">Element const *&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc_1_1Params.html#a088d18e084a3bd3c60ef12069b70b03c">ptr</a></td></tr>
<tr class="separator:a088d18e084a3bd3c60ef12069b70b03c"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a24b17f2db455bfb0d86f6534c6850766"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::Params::Params </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a34d3d2fa3894cc57964ac1af16a8612a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::Params::Params </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a>&#160;</td>
          <td class="paramname"><em>view_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">Element const *&#160;</td>
          <td class="paramname"><em>ptr_</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">view_</td><td>destination tensor </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a088d18e084a3bd3c60ef12069b70b03c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">Element const* <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::Params::ptr</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aaaeac91c7344b4b29b290ffd095ef57f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element , typename Layout &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html#a8510e634ed1a482dc6b4baeaac881caa">TensorView</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1TensorCopyDiagonalInFunc.html">cutlass::reference::device::detail::TensorCopyDiagonalInFunc</a>&lt; Element, Layout &gt;::Params::view</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="device_2tensor__fill_8h_source.html">device/tensor_fill.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
