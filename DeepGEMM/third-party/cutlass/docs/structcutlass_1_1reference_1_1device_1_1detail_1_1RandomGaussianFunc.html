<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::device::detail::RandomGaussianFunc&lt; Element &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device.html">device</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1device_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">RandomGaussianFunc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::device::detail::RandomGaussianFunc&lt; Element &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="device_2tensor__fill_8h_source.html">tensor_fill.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::device::detail::RandomGaussianFunc&lt; Element &gt;:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_3_01Element_01_4_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_3_01Element_01_4_coll__map" id="cutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_3_01Element_01_4_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_1_1Params.html">Params</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameters structure.  <a href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_1_1Params.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:ac7cf68adaae0b16b1633a4e3f5d79aa5"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#ac7cf68adaae0b16b1633a4e3f5d79aa5">FloatType</a> = typename std::conditional&lt;(sizeof(Element) &gt; 4), double, float &gt;::type</td></tr>
<tr class="separator:ac7cf68adaae0b16b1633a4e3f5d79aa5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad4d61c5ff2534d18ce26fed88a17c937"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#ad4d61c5ff2534d18ce26fed88a17c937">IntType</a> = typename std::conditional&lt;(sizeof(Element) &gt; 4), int64_t, int &gt;::type</td></tr>
<tr class="separator:ad4d61c5ff2534d18ce26fed88a17c937"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a38bf4f3bfe2df73c264a23f3956a65fd"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#a38bf4f3bfe2df73c264a23f3956a65fd">RandomGaussianFunc</a> (<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_1_1Params.html">Params</a> const &amp;<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#acd40b7369356ac0ad4e83db8742677a5">params</a>)</td></tr>
<tr class="memdesc:a38bf4f3bfe2df73c264a23f3956a65fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Device-side initialization of RNG.  <a href="#a38bf4f3bfe2df73c264a23f3956a65fd">More...</a><br /></td></tr>
<tr class="separator:a38bf4f3bfe2df73c264a23f3956a65fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8926a03c72ad2d9720dd1e4f39e0496e"><td class="memItemLeft" align="right" valign="top">CUTLASS_DEVICE Element&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#a8926a03c72ad2d9720dd1e4f39e0496e">operator()</a> ()</td></tr>
<tr class="memdesc:a8926a03c72ad2d9720dd1e4f39e0496e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute random value and update RNG state.  <a href="#a8926a03c72ad2d9720dd1e4f39e0496e">More...</a><br /></td></tr>
<tr class="separator:a8926a03c72ad2d9720dd1e4f39e0496e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:acd40b7369356ac0ad4e83db8742677a5"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_1_1Params.html">Params</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#acd40b7369356ac0ad4e83db8742677a5">params</a></td></tr>
<tr class="memdesc:acd40b7369356ac0ad4e83db8742677a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameters object.  <a href="#acd40b7369356ac0ad4e83db8742677a5">More...</a><br /></td></tr>
<tr class="separator:acd40b7369356ac0ad4e83db8742677a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a52dd271db62c366ac41e84407b9176c3"><td class="memItemLeft" align="right" valign="top">curandState_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#a52dd271db62c366ac41e84407b9176c3">rng_state</a></td></tr>
<tr class="memdesc:a52dd271db62c366ac41e84407b9176c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">RNG state object.  <a href="#a52dd271db62c366ac41e84407b9176c3">More...</a><br /></td></tr>
<tr class="separator:a52dd271db62c366ac41e84407b9176c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ac7cf68adaae0b16b1633a4e3f5d79aa5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::device::detail::RandomGaussianFunc</a>&lt; Element &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#ac7cf68adaae0b16b1633a4e3f5d79aa5">FloatType</a> =  typename std::conditional&lt;(sizeof(Element) &gt; 4), double, float&gt;::type</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad4d61c5ff2534d18ce26fed88a17c937"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::device::detail::RandomGaussianFunc</a>&lt; Element &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html#ad4d61c5ff2534d18ce26fed88a17c937">IntType</a> =  typename std::conditional&lt;(sizeof(Element) &gt; 4), int64_t, int&gt;::type</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a38bf4f3bfe2df73c264a23f3956a65fd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::device::detail::RandomGaussianFunc</a>&lt; Element &gt;::<a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">RandomGaussianFunc</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_1_1Params.html">Params</a> const &amp;&#160;</td>
          <td class="paramname"><em>params</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a8926a03c72ad2d9720dd1e4f39e0496e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">CUTLASS_DEVICE Element <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::device::detail::RandomGaussianFunc</a>&lt; Element &gt;::operator() </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="acd40b7369356ac0ad4e83db8742677a5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc_1_1Params.html">Params</a> <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::device::detail::RandomGaussianFunc</a>&lt; Element &gt;::params</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a52dd271db62c366ac41e84407b9176c3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Element &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">curandState_t <a class="el" href="structcutlass_1_1reference_1_1device_1_1detail_1_1RandomGaussianFunc.html">cutlass::reference::device::detail::RandomGaussianFunc</a>&lt; Element &gt;::rng_state</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="device_2tensor__fill_8h_source.html">device/tensor_fill.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
