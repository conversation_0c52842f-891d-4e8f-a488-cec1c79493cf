<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reference::host::detail::TensorCopyIf&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference.html">reference</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host.html">host</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reference_1_1host_1_1detail.html">detail</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">TensorCopyIf</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reference::host::detail::TensorCopyIf&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Helper to conditionally copy between tensor views.  
</p>

<p><code>#include &lt;<a class="el" href="tensor__copy_8h_source.html">tensor_copy.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reference::host::detail::TensorCopyIf&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf__coll__graph.png" border="0" usemap="#cutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_3_01DstElement_00_01DstLayout_00_01SrcElement_00_01SrcLayout_00_01F_01_4_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_3_01DstElement_00_01DstLayout_00_01SrcElement_00_01SrcLayout_00_01F_01_4_coll__map" id="cutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_3_01DstElement_00_01DstLayout_00_01SrcElement_00_01SrcLayout_00_01F_01_4_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:af25c3242565e3600b4ab447c2fc47f2d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#af25c3242565e3600b4ab447c2fc47f2d">DstTensorView</a> = <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; DstElement, DstLayout &gt;</td></tr>
<tr class="separator:af25c3242565e3600b4ab447c2fc47f2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b5dd5bf877993aebdba48dd416ba6dd"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a3b5dd5bf877993aebdba48dd416ba6dd">SrcTensorView</a> = <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt; SrcElement, SrcLayout &gt;</td></tr>
<tr class="separator:a3b5dd5bf877993aebdba48dd416ba6dd"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:aa21edde0e94e5f2c14598ab0d3fc5311"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#aa21edde0e94e5f2c14598ab0d3fc5311">TensorCopyIf</a> ()</td></tr>
<tr class="separator:aa21edde0e94e5f2c14598ab0d3fc5311"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9ee72419fd72488215e17ba746cc699d"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a9ee72419fd72488215e17ba746cc699d">TensorCopyIf</a> (<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#af25c3242565e3600b4ab447c2fc47f2d">DstTensorView</a> const &amp;dst_, <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a3b5dd5bf877993aebdba48dd416ba6dd">SrcTensorView</a> const &amp;src_, F const &amp;convert_)</td></tr>
<tr class="separator:a9ee72419fd72488215e17ba746cc699d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2df07db0906c5cbed9f4eea92718e0e"><td class="memItemLeft" align="right" valign="top">void&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ac2df07db0906c5cbed9f4eea92718e0e">operator()</a> (<a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; DstLayout::kRank &gt; const &amp;coord)</td></tr>
<tr class="memdesc:ac2df07db0906c5cbed9f4eea92718e0e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Copies based on destination and source bounds.  <a href="#ac2df07db0906c5cbed9f4eea92718e0e">More...</a><br /></td></tr>
<tr class="separator:ac2df07db0906c5cbed9f4eea92718e0e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a0d94963e36e238233ddb550845b37004"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#af25c3242565e3600b4ab447c2fc47f2d">DstTensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a0d94963e36e238233ddb550845b37004">dst</a></td></tr>
<tr class="separator:a0d94963e36e238233ddb550845b37004"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a153ae0606432a65e3a4aa0017936181f"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a3b5dd5bf877993aebdba48dd416ba6dd">SrcTensorView</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a153ae0606432a65e3a4aa0017936181f">src</a></td></tr>
<tr class="separator:a153ae0606432a65e3a4aa0017936181f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9e5902883076ca684487d276a79c47e"><td class="memItemLeft" align="right" valign="top">F&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ad9e5902883076ca684487d276a79c47e">convert</a></td></tr>
<tr class="separator:ad9e5902883076ca684487d276a79c47e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="af25c3242565e3600b4ab447c2fc47f2d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#af25c3242565e3600b4ab447c2fc47f2d">DstTensorView</a> =  <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt;DstElement, DstLayout&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3b5dd5bf877993aebdba48dd416ba6dd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a3b5dd5bf877993aebdba48dd416ba6dd">SrcTensorView</a> =  <a class="el" href="classcutlass_1_1TensorView.html">TensorView</a>&lt;SrcElement, SrcLayout&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="aa21edde0e94e5f2c14598ab0d3fc5311"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">TensorCopyIf</a> </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9ee72419fd72488215e17ba746cc699d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::<a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">TensorCopyIf</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#af25c3242565e3600b4ab447c2fc47f2d">DstTensorView</a> const &amp;&#160;</td>
          <td class="paramname"><em>dst_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a3b5dd5bf877993aebdba48dd416ba6dd">SrcTensorView</a> const &amp;&#160;</td>
          <td class="paramname"><em>src_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">F const &amp;&#160;</td>
          <td class="paramname"><em>convert_</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ac2df07db0906c5cbed9f4eea92718e0e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1Coord.html">Coord</a>&lt; DstLayout::kRank &gt; const &amp;&#160;</td>
          <td class="paramname"><em>coord</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="ad9e5902883076ca684487d276a79c47e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">F <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::convert</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a0d94963e36e238233ddb550845b37004"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#af25c3242565e3600b4ab447c2fc47f2d">DstTensorView</a> <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::dst</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a153ae0606432a65e3a4aa0017936181f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename DstElement , typename DstLayout , typename SrcElement , typename SrcLayout , typename F &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a3b5dd5bf877993aebdba48dd416ba6dd">SrcTensorView</a> <a class="el" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a>&lt; DstElement, DstLayout, SrcElement, SrcLayout, F &gt;::src</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="tensor__copy_8h_source.html">tensor_copy.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
