<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_coord.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_coord.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__coord_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="coord_8h.html">cutlass/coord.h</a>&quot;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html">   38</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> : <span class="keyword">public</span> <a class="code" href="structcutlass_1_1Coord.html">Coord</a>&lt;4&gt; {</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a367dd7e307c7e8e2aa7396d027e8891e">   41</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Coord.html">Base</a> = <a class="code" href="structcutlass_1_1Coord.html">Coord&lt;4&gt;</a>;</div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">   44</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">Base::Index</a>;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a779bf9ea896ac4ae9d4def10cd23eb45">   47</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a779bf9ea896ac4ae9d4def10cd23eb45">LongIndex</a> = <span class="keyword">typename</span> <a class="code" href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">Base::LongIndex</a>;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div><div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#acb0b48b015b75e2d7a226a69f5a2f3b8">   50</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#acb0b48b015b75e2d7a226a69f5a2f3b8">kN</a> = 0;</div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#aa4014fb6f869b2b5c16796f4435eb110">   53</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#aa4014fb6f869b2b5c16796f4435eb110">kH</a> = 1;</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a01e55a99e690d697ca62cfaeb4bcde9f">   56</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a01e55a99e690d697ca62cfaeb4bcde9f">kW</a> = 2;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a538809c6f5ee032adf4558cd004d988d">   59</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a538809c6f5ee032adf4558cd004d988d">kC</a> = 3;</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">   67</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">Tensor4DCoord</a>() { }</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#afac3bfcfde4408c922ca25c965a40cd7">   71</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#afac3bfcfde4408c922ca25c965a40cd7">Tensor4DCoord</a>(<a class="code" href="structcutlass_1_1Coord.html">Coord&lt;4&gt;</a> <span class="keyword">const</span> &amp;coord): <a class="code" href="structcutlass_1_1Coord.html">Base</a>(coord) { }</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#aac1d0a33901e2bfb88eb277a594bcd0c">   75</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#aac1d0a33901e2bfb88eb277a594bcd0c">Tensor4DCoord</a>(<a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>, <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>, <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>, <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>): <a class="code" href="structcutlass_1_1Coord.html">Base</a>(<a class="code" href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">make_Coord</a>(n, h, w, c)) { }</div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">   79</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <span class="keyword">const</span> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">n</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kN); }</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a8f3d209442262c674f0bde0257ef1792">   83</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a8f3d209442262c674f0bde0257ef1792">n</a>() { <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kN); }</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">   87</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <span class="keyword">const</span> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">h</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kH); }</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#ae399c4159fb4e799c42bd882df2ccce7">   91</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae399c4159fb4e799c42bd882df2ccce7">h</a>() { <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kH); }</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">   95</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <span class="keyword">const</span> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">w</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kW); }</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a3b391bf3ec3db6eec31eb23d5ff7fd21">   99</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a3b391bf3ec3db6eec31eb23d5ff7fd21">w</a>() { <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kW); }</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">  103</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> <span class="keyword">const</span> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">c</a>()<span class="keyword"> const </span>{ <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kC); }</div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a494c8f38161b2d767f9497e751467699">  107</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">Index</a> &amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a494c8f38161b2d767f9497e751467699">c</a>() { <span class="keywordflow">return</span> this-&gt;<a class="code" href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">at</a>(kC); }</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="comment">// Coord operators</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a28448ff7ebd10f76954d012e7ae9bcd8">  115</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a28448ff7ebd10f76954d012e7ae9bcd8">operator+</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b)<span class="keyword"> const </span>{</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">Tensor4DCoord</a>(Base::operator+(b));</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  }</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00121"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a9f850c2e2a7b4cda1e58a04884d8be47">  121</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a9f850c2e2a7b4cda1e58a04884d8be47">operator-</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b)<span class="keyword"> const </span>{</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">Tensor4DCoord</a>(Base::operator-(b));</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;  }</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#aa9fd53334c3a6fdcce9b83896355d429">  127</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#aa9fd53334c3a6fdcce9b83896355d429">operator*</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b)<span class="keyword"> const </span>{</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">Tensor4DCoord</a>(Base::operator*(b));</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;  }</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#ab2fd81b93d9130ff969b783dbaab54b2">  133</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab2fd81b93d9130ff969b783dbaab54b2">operator/</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b)<span class="keyword"> const </span>{</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">Tensor4DCoord</a>(Base::operator/(b));</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  }</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00139"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#af5f312484425767c77f0192cc89eef3d">  139</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#af5f312484425767c77f0192cc89eef3d">operator+=</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b) {</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;    <a class="code" href="structcutlass_1_1Coord.html#acb799faf60a17b708d0802f9e23c812f">Base::operator+=</a>(b);</div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  }</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00146"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#a05798b41f0fbaa92f766902bac286609">  146</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#a05798b41f0fbaa92f766902bac286609">operator-=</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b) {</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    <a class="code" href="structcutlass_1_1Coord.html#a15ac170c861b34d418432aeb62ea86e0">Base::operator-=</a>(b);</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;  }</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00153"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#ab56a0b2352264f7a3753b621d1d850d6">  153</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab56a0b2352264f7a3753b621d1d850d6">operator*=</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b) {</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;    <a class="code" href="structcutlass_1_1Coord.html#a00e618bc944d355badf67c0edd791412">Base::operator*=</a>(b);</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;  }</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;  <a class="code" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="line"><a name="l00160"></a><span class="lineno"><a class="line" href="structcutlass_1_1Tensor4DCoord.html#ab591c052af780e65a77ea3e0f33d46aa">  160</a></span>&#160;  <a class="code" href="structcutlass_1_1Tensor4DCoord.html">Tensor4DCoord</a>&amp; <a class="code" href="structcutlass_1_1Tensor4DCoord.html#ab591c052af780e65a77ea3e0f33d46aa">operator/=</a>(<a class="code" href="structcutlass_1_1Coord.html">Base</a> <span class="keyword">const</span>&amp; b) {</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;    <a class="code" href="structcutlass_1_1Coord.html#af515e669363986dbbd60951ea6b69e14">Base::operator/=</a>(b);</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;    <span class="keywordflow">return</span> *<span class="keyword">this</span>;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  }</div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;};</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;</div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;</div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a8f3d209442262c674f0bde0257ef1792"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a8f3d209442262c674f0bde0257ef1792">cutlass::Tensor4DCoord::n</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index &amp; n()</div><div class="ttdoc">Returns the batch of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:83</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html">cutlass::Tensor4DCoord</a></div><div class="ttdoc">Defines a canonical 4D coordinate used by tensor operations. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:38</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ab2fd81b93d9130ff969b783dbaab54b2"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ab2fd81b93d9130ff969b783dbaab54b2">cutlass::Tensor4DCoord::operator/</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord operator/(Base const &amp;b) const </div><div class="ttdoc">Element-wise division. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:133</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_a00e618bc944d355badf67c0edd791412"><div class="ttname"><a href="structcutlass_1_1Coord.html#a00e618bc944d355badf67c0edd791412">cutlass::Coord&lt; 4 &gt;::operator*=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord &amp; operator*=(Coord const &amp;b)</div><div class="ttdoc">In-place multiplication. </div><div class="ttdef"><b>Definition:</b> coord.h:222</div></div>
<div class="ttc" id="coord_8h_html"><div class="ttname"><a href="coord_8h.html">coord.h</a></div><div class="ttdoc">A Coord is a coordinate of arbitrary rank into a tensor or matrix. </div></div>
<div class="ttc" id="namespacecutlass_html_a7419519fa453a121dfa5f26bf87318d9"><div class="ttname"><a href="namespacecutlass.html#a7419519fa453a121dfa5f26bf87318d9">cutlass::make_Coord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord&lt; 1 &gt; make_Coord(int _0)</div><div class="ttdoc">Helper to make a 2-element coordinate. </div><div class="ttdef"><b>Definition:</b> coord.h:387</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_a15ac170c861b34d418432aeb62ea86e0"><div class="ttname"><a href="structcutlass_1_1Coord.html#a15ac170c861b34d418432aeb62ea86e0">cutlass::Coord&lt; 4 &gt;::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord &amp; operator-=(Coord const &amp;b)</div><div class="ttdoc">In-place subtraction. </div><div class="ttdef"><b>Definition:</b> coord.h:213</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a3b391bf3ec3db6eec31eb23d5ff7fd21"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a3b391bf3ec3db6eec31eb23d5ff7fd21">cutlass::Tensor4DCoord::w</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index &amp; w()</div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:99</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ae3136dc898c4ef079e73b51b1850ba7e"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ae3136dc898c4ef079e73b51b1850ba7e">cutlass::Tensor4DCoord::w</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; w() const </div><div class="ttdoc">Returns the column of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:95</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_a7a89e5661ef391dd9f4fe81f0c982b75"><div class="ttname"><a href="structcutlass_1_1Coord.html#a7a89e5661ef391dd9f4fe81f0c982b75">cutlass::Coord&lt; 4 &gt;::Index</a></div><div class="ttdeci">int Index</div><div class="ttdoc">Index type used to store elements. </div><div class="ttdef"><b>Definition:</b> coord.h:55</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_aa9fd53334c3a6fdcce9b83896355d429"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#aa9fd53334c3a6fdcce9b83896355d429">cutlass::Tensor4DCoord::operator*</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord operator*(Base const &amp;b) const </div><div class="ttdoc">Element-wise multiplication. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:127</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a9a9c773b2bfec43d1722fd7a490fd436"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a9a9c773b2bfec43d1722fd7a490fd436">cutlass::Tensor4DCoord::Tensor4DCoord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord()</div><div class="ttdoc">Default ctor. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:67</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ab0f58e5f54b42534fca77a662c78c7ad"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ab0f58e5f54b42534fca77a662c78c7ad">cutlass::Tensor4DCoord::c</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; c() const </div><div class="ttdoc">Returns the channel of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:103</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a538809c6f5ee032adf4558cd004d988d"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a538809c6f5ee032adf4558cd004d988d">cutlass::Tensor4DCoord::kC</a></div><div class="ttdeci">static int const kC</div><div class="ttdoc">Channels dimension. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:59</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a494c8f38161b2d767f9497e751467699"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a494c8f38161b2d767f9497e751467699">cutlass::Tensor4DCoord::c</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index &amp; c()</div><div class="ttdoc">Returns the channel of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:107</div></div>
<div class="ttc" id="cutlass_8h_html_a28c2443a142676d3d71effdae1a986b1"><div class="ttname"><a href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a></div><div class="ttdeci">#define CUTLASS_HOST_DEVICE</div><div class="ttdef"><b>Definition:</b> cutlass.h:89</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a779bf9ea896ac4ae9d4def10cd23eb45"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a779bf9ea896ac4ae9d4def10cd23eb45">cutlass::Tensor4DCoord::LongIndex</a></div><div class="ttdeci">typename Base::LongIndex LongIndex</div><div class="ttdoc">LongIndex type. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:47</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_af515e669363986dbbd60951ea6b69e14"><div class="ttname"><a href="structcutlass_1_1Coord.html#af515e669363986dbbd60951ea6b69e14">cutlass::Coord&lt; 4 &gt;::operator/=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord &amp; operator/=(Coord const &amp;b)</div><div class="ttdoc">In-place division. </div><div class="ttdef"><b>Definition:</b> coord.h:231</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ab591c052af780e65a77ea3e0f33d46aa"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ab591c052af780e65a77ea3e0f33d46aa">cutlass::Tensor4DCoord::operator/=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord &amp; operator/=(Base const &amp;b)</div><div class="ttdoc">In-place division. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:160</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a05798b41f0fbaa92f766902bac286609"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a05798b41f0fbaa92f766902bac286609">cutlass::Tensor4DCoord::operator-=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord &amp; operator-=(Base const &amp;b)</div><div class="ttdoc">In-place subtraction. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:146</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord</a></div><div class="ttdoc">Statically-sized array specifying Coords within a tensor. </div><div class="ttdef"><b>Definition:</b> coord.h:43</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a2fa718218c21df006b71d9325f1ddb5a"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a2fa718218c21df006b71d9325f1ddb5a">cutlass::Tensor4DCoord::n</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; n() const </div><div class="ttdoc">Returns the batch of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:79</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_afac3bfcfde4408c922ca25c965a40cd7"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#afac3bfcfde4408c922ca25c965a40cd7">cutlass::Tensor4DCoord::Tensor4DCoord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord(Coord&lt; 4 &gt; const &amp;coord)</div><div class="ttdoc">Constructs from Coord&lt;4&gt; </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:71</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ae399c4159fb4e799c42bd882df2ccce7"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ae399c4159fb4e799c42bd882df2ccce7">cutlass::Tensor4DCoord::h</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index &amp; h()</div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:91</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_acb0b48b015b75e2d7a226a69f5a2f3b8"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#acb0b48b015b75e2d7a226a69f5a2f3b8">cutlass::Tensor4DCoord::kN</a></div><div class="ttdeci">static int const kN</div><div class="ttdoc">Batch dimension. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:50</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_aac1d0a33901e2bfb88eb277a594bcd0c"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#aac1d0a33901e2bfb88eb277a594bcd0c">cutlass::Tensor4DCoord::Tensor4DCoord</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord(Index n, Index h, Index w, Index c)</div><div class="ttdoc">Helper to construct from N, H, W, and C. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:75</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a01e55a99e690d697ca62cfaeb4bcde9f"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a01e55a99e690d697ca62cfaeb4bcde9f">cutlass::Tensor4DCoord::kW</a></div><div class="ttdeci">static int const kW</div><div class="ttdoc">Width dimension. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:56</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_af5f312484425767c77f0192cc89eef3d"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#af5f312484425767c77f0192cc89eef3d">cutlass::Tensor4DCoord::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord &amp; operator+=(Base const &amp;b)</div><div class="ttdoc">In-place addition. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:139</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_acb799faf60a17b708d0802f9e23c812f"><div class="ttname"><a href="structcutlass_1_1Coord.html#acb799faf60a17b708d0802f9e23c812f">cutlass::Coord&lt; 4 &gt;::operator+=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Coord &amp; operator+=(Coord const &amp;b)</div><div class="ttdoc">In-place addition. </div><div class="ttdef"><b>Definition:</b> coord.h:204</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a71dda571a04037e564f238bb9a76f213"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a71dda571a04037e564f238bb9a76f213">cutlass::Tensor4DCoord::h</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index const &amp; h() const </div><div class="ttdoc">Returns the row of the coordinate. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:87</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_ab61db7c2bfacaf0b7ce465e70d48c44f"><div class="ttname"><a href="structcutlass_1_1Coord.html#ab61db7c2bfacaf0b7ce465e70d48c44f">cutlass::Coord&lt; 4 &gt;::LongIndex</a></div><div class="ttdeci">int64_t LongIndex</div><div class="ttdoc">Type used to represent linear offsets. </div><div class="ttdef"><b>Definition:</b> coord.h:58</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_ab56a0b2352264f7a3753b621d1d850d6"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#ab56a0b2352264f7a3753b621d1d850d6">cutlass::Tensor4DCoord::operator*=</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord &amp; operator*=(Base const &amp;b)</div><div class="ttdoc">In-place multiplication. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:153</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html_a8a65128c86b236cd2bea875b85a34bc1"><div class="ttname"><a href="structcutlass_1_1Coord.html#a8a65128c86b236cd2bea875b85a34bc1">cutlass::Coord&lt; 4 &gt;::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Index &amp; at()</div><div class="ttdoc">Gets the index of a given Coord element. </div><div class="ttdef"><b>Definition:</b> coord.h:255</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a28448ff7ebd10f76954d012e7ae9bcd8"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a28448ff7ebd10f76954d012e7ae9bcd8">cutlass::Tensor4DCoord::operator+</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord operator+(Base const &amp;b) const </div><div class="ttdoc">Element-wise addition. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:115</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_aa4014fb6f869b2b5c16796f4435eb110"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#aa4014fb6f869b2b5c16796f4435eb110">cutlass::Tensor4DCoord::kH</a></div><div class="ttdeci">static int const kH</div><div class="ttdoc">Height dimension. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:53</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a4f63c0cc6b642e80624beafb6c3390a1"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a4f63c0cc6b642e80624beafb6c3390a1">cutlass::Tensor4DCoord::Index</a></div><div class="ttdeci">typename Base::Index Index</div><div class="ttdoc">Index type. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:44</div></div>
<div class="ttc" id="structcutlass_1_1Tensor4DCoord_html_a9f850c2e2a7b4cda1e58a04884d8be47"><div class="ttname"><a href="structcutlass_1_1Tensor4DCoord.html#a9f850c2e2a7b4cda1e58a04884d8be47">cutlass::Tensor4DCoord::operator-</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Tensor4DCoord operator-(Base const &amp;b) const </div><div class="ttdoc">Element-wise subtraction. </div><div class="ttdef"><b>Definition:</b> tensor_coord.h:121</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
