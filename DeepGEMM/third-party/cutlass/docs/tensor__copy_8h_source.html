<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_copy.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li><li class="navelem"><a class="el" href="dir_01de8928c960cafb028e5f164701e1de.html">reference</a></li><li class="navelem"><a class="el" href="dir_b790a865367d69962c5919afdba4a959.html">host</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_copy.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__copy_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="comment">/* \file</span></div><div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="comment">  \brief Defines host-side elementwise operations on TensorView.</span></div><div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;<span class="comment">*/</span></div><div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;</div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="comment">// Standard Library includes</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &lt;utility&gt;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="comment">// Cutlass includes</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="cutlass_8h.html">cutlass/cutlass.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="host_2tensor__foreach_8h.html">tensor_foreach.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">namespace </span>reference {</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">namespace </span>host {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="keyword">namespace </span>detail {</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;</div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keyword">typename</span> DstElement,</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;  <span class="keyword">typename</span> SrcElement</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;&gt;</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html">   51</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html">TrivialConvert</a> {</div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html#a329020d42d4ee1fdcdee82d70432d1ed">   53</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html#a329020d42d4ee1fdcdee82d70432d1ed">TrivialConvert</a>() { }</div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html#aaaf16d4fc00a6c2a507cb964b41b3f84">   55</a></span>&#160;  DstElement <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html#aaaf16d4fc00a6c2a507cb964b41b3f84">operator()</a>(SrcElement src)<span class="keyword"> const </span>{</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;    <span class="keywordflow">return</span> DstElement(src);</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  }</div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;};</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">typename</span> DstElement,</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;  <span class="keyword">typename</span> DstLayout,</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;  <span class="keyword">typename</span> SrcElement,</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;  <span class="keyword">typename</span> SrcLayout,</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;  <span class="keyword">typename</span> F</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;&gt;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">   68</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">TensorCopyIf</a> {</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#af25c3242565e3600b4ab447c2fc47f2d">   70</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">DstTensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;DstElement, DstLayout&gt;</a>;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a3b5dd5bf877993aebdba48dd416ba6dd">   71</a></span>&#160;  <span class="keyword">using</span> <a class="code" href="classcutlass_1_1TensorView.html">SrcTensorView</a> = <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;SrcElement, SrcLayout&gt;</a>;</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="comment">// Data members</span></div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a0d94963e36e238233ddb550845b37004">   77</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">DstTensorView</a> <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a0d94963e36e238233ddb550845b37004">dst</a>;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a153ae0606432a65e3a4aa0017936181f">   78</a></span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">SrcTensorView</a> <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a153ae0606432a65e3a4aa0017936181f">src</a>;</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ad9e5902883076ca684487d276a79c47e">   79</a></span>&#160;  F <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ad9e5902883076ca684487d276a79c47e">convert</a>;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;  <span class="comment">// Methods</span></div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#aa21edde0e94e5f2c14598ab0d3fc5311">   85</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#aa21edde0e94e5f2c14598ab0d3fc5311">TensorCopyIf</a>() { }</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a9ee72419fd72488215e17ba746cc699d">   87</a></span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a9ee72419fd72488215e17ba746cc699d">TensorCopyIf</a>(</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html">DstTensorView</a> <span class="keyword">const</span> &amp;dst_, </div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;    <a class="code" href="classcutlass_1_1TensorView.html">SrcTensorView</a> <span class="keyword">const</span> &amp;src_,</div><div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;    F <span class="keyword">const</span> &amp;convert_): dst(dst_), src(src_), convert(convert_) {}</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div><div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ac2df07db0906c5cbed9f4eea92718e0e">   93</a></span>&#160;  <span class="keywordtype">void</span> <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ac2df07db0906c5cbed9f4eea92718e0e">operator()</a>(<a class="code" href="structcutlass_1_1Coord.html">Coord&lt;DstLayout::kRank&gt;</a> <span class="keyword">const</span> &amp;coord) {</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <span class="keywordflow">if</span> (dst.<a class="code" href="classcutlass_1_1TensorView.html#a6a6a1f99d06abd8fb3f5a8e4e0fea25e">contains</a>(coord) &amp;&amp; src.<a class="code" href="classcutlass_1_1TensorView.html#a6a6a1f99d06abd8fb3f5a8e4e0fea25e">contains</a>(coord)) {</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;      dst.<a class="code" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">at</a>(coord) = convert(src.<a class="code" href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">at</a>(coord));</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;    }</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;  }</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;};</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;} <span class="comment">// namespace detail</span></div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;  <span class="keyword">typename</span> DstElement,          </div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <span class="keyword">typename</span> DstLayout,           </div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;  <span class="keyword">typename</span> SrcElement,          </div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="keyword">typename</span> SrcLayout,           </div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="keyword">typename</span> F                    </div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;&gt;</div><div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">  112</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;DstElement, DstLayout&gt;</a> dst,</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;SrcElement, SrcLayout&gt;</a> src,</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;  F <span class="keyword">const</span> &amp;transform) {</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <span class="keyword">using</span> CopyIf = <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">detail::TensorCopyIf</a>&lt;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;    DstElement,</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;    DstLayout,</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;    SrcElement,</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;    SrcLayout,</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;    F&gt;;</div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;  CopyIf copy_if(dst, src, transform);</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1host.html#a8c798c04df572b34e3ed3976d69f993d">TensorForEach</a>(dst.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>(), copy_if);</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;}</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div><div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;  <span class="keyword">typename</span> DstElement,          </div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;  <span class="keyword">typename</span> DstLayout,           </div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;  <span class="keyword">typename</span> SrcElement,          </div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <span class="keyword">typename</span> SrcLayout,           </div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;  <span class="keyword">typename</span> F                    </div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;&gt;</div><div class="line"><a name="l00141"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#a32903a34034cfe040157a5cd48c325ce">  141</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(</div><div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;DstElement, DstLayout&gt;</a> dst,</div><div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;SrcElement, SrcLayout&gt;</a> src,</div><div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;  F <span class="keyword">const</span> &amp;transform) {</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;  <span class="keyword">using</span> CopyIf = <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">detail::TensorCopyIf</a>&lt;</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;    DstElement,</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;    DstLayout,</div><div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;    SrcElement,</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;    SrcLayout,</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;    F&gt;;</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;SrcElement, SrcLayout&gt;</a> src_view(src, dst.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>());</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;  CopyIf copy_if(dst, src_view, transform);</div><div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div><div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1host.html#a8c798c04df572b34e3ed3976d69f993d">TensorForEach</a>(dst.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>(), copy_if);</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;}</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;  <span class="keyword">typename</span> DstElement,          </div><div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;  <span class="keyword">typename</span> DstLayout,           </div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;  <span class="keyword">typename</span> SrcElement,          </div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;  <span class="keyword">typename</span> SrcLayout,           </div><div class="line"><a name="l00167"></a><span class="lineno">  167</span>&#160;  <span class="keyword">typename</span> F                    </div><div class="line"><a name="l00168"></a><span class="lineno">  168</span>&#160;&gt;</div><div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#a723c4026b4a73a6050aa203aee95de84">  169</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;DstElement, DstLayout&gt;</a> dst,</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;SrcElement, SrcLayout&gt;</a> src,</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;  F <span class="keyword">const</span> &amp;transform) {</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;  <span class="keyword">using</span> CopyIf = <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">detail::TensorCopyIf</a>&lt;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;    DstElement,</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;    DstLayout,</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;    SrcElement,</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;    SrcLayout,</div><div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;    F&gt;;</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;DstElement, DstLayout&gt;</a> dst_view(dst, src.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>());</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;</div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;  CopyIf copy_if(dst_view, src, transform);</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1host.html#a8c798c04df572b34e3ed3976d69f993d">TensorForEach</a>(src.<a class="code" href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">extent</a>(), copy_if);</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;}</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  <span class="keyword">typename</span> DstElement,          </div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;  <span class="keyword">typename</span> DstLayout,           </div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;  <span class="keyword">typename</span> SrcElement,          </div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;  <span class="keyword">typename</span> SrcLayout            </div><div class="line"><a name="l00197"></a><span class="lineno">  197</span>&#160;&gt;</div><div class="line"><a name="l00198"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#a1ffea013419e1ac514797633dd46d6a6">  198</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;DstElement, DstLayout&gt;</a> dst,</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;SrcElement, SrcLayout&gt;</a> src) {</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html">detail::TrivialConvert&lt;DstElement, SrcElement&gt;</a> convert;</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(dst, src, convert);</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;}</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;  <span class="keyword">typename</span> DstElement,          </div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;  <span class="keyword">typename</span> DstLayout,           </div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;  <span class="keyword">typename</span> SrcElement,          </div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;  <span class="keyword">typename</span> SrcLayout,           </div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;  <span class="keyword">typename</span> F                    </div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;&gt;</div><div class="line"><a name="l00218"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#aae15ab711d7be5074953df3fa62bae0d">  218</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(</div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;DstElement, DstLayout&gt;</a> dst,</div><div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;SrcElement, SrcLayout&gt;</a> src) {</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html">detail::TrivialConvert&lt;DstElement, SrcElement&gt;</a> convert;</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(dst, src, convert);</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;}</div><div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;  <span class="keyword">typename</span> DstElement,          </div><div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;  <span class="keyword">typename</span> DstLayout,           </div><div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;  <span class="keyword">typename</span> SrcElement,          </div><div class="line"><a name="l00235"></a><span class="lineno">  235</span>&#160;  <span class="keyword">typename</span> SrcLayout            </div><div class="line"><a name="l00236"></a><span class="lineno">  236</span>&#160;&gt;</div><div class="line"><a name="l00237"></a><span class="lineno"><a class="line" href="namespacecutlass_1_1reference_1_1host.html#a513f4fef76bc1a6b34b39f0d6bd7c48a">  237</a></span>&#160;<span class="keywordtype">void</span> <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(</div><div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;  <a class="code" href="classcutlass_1_1TensorRef.html">TensorRef&lt;DstElement, DstLayout&gt;</a> dst,</div><div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;  <a class="code" href="classcutlass_1_1TensorView.html">TensorView&lt;SrcElement, SrcLayout&gt;</a> src) {</div><div class="line"><a name="l00240"></a><span class="lineno">  240</span>&#160;</div><div class="line"><a name="l00241"></a><span class="lineno">  241</span>&#160;  <a class="code" href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html">detail::TrivialConvert&lt;DstElement, SrcElement&gt;</a> convert;</div><div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;</div><div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;  <a class="code" href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">TensorCopy</a>(dst, src, convert);</div><div class="line"><a name="l00244"></a><span class="lineno">  244</span>&#160;}</div><div class="line"><a name="l00245"></a><span class="lineno">  245</span>&#160;</div><div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;</div><div class="line"><a name="l00248"></a><span class="lineno">  248</span>&#160;} <span class="comment">// namespace host</span></div><div class="line"><a name="l00249"></a><span class="lineno">  249</span>&#160;} <span class="comment">// namespace reference</span></div><div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a7d3914dd5042c9c40be9e21a7b4e9ece"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a7d3914dd5042c9c40be9e21a7b4e9ece">cutlass::TensorView::extent</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE TensorCoord const &amp; extent() const </div><div class="ttdoc">Returns the extent of the view (the size along each logical dimension). </div><div class="ttdef"><b>Definition:</b> tensor_view.h:167</div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1host_html_ab32bea7b552a408f93fc2f153b44fcb8"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1host.html#ab32bea7b552a408f93fc2f153b44fcb8">cutlass::reference::host::TensorCopy</a></div><div class="ttdeci">void TensorCopy(TensorView&lt; DstElement, DstLayout &gt; dst, TensorView&lt; SrcElement, SrcLayout &gt; src, F const &amp;transform)</div><div class="ttdoc">Copies elements from one tensor view into another, satisfying bounds of each tensor. </div><div class="ttdef"><b>Definition:</b> tensor_copy.h:112</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_html_aa21edde0e94e5f2c14598ab0d3fc5311"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#aa21edde0e94e5f2c14598ab0d3fc5311">cutlass::reference::host::detail::TensorCopyIf::TensorCopyIf</a></div><div class="ttdeci">TensorCopyIf()</div><div class="ttdef"><b>Definition:</b> tensor_copy.h:85</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert_html_aaaf16d4fc00a6c2a507cb964b41b3f84"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html#aaaf16d4fc00a6c2a507cb964b41b3f84">cutlass::reference::host::detail::TrivialConvert::operator()</a></div><div class="ttdeci">DstElement operator()(SrcElement src) const </div><div class="ttdef"><b>Definition:</b> tensor_copy.h:55</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_html_ac2df07db0906c5cbed9f4eea92718e0e"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ac2df07db0906c5cbed9f4eea92718e0e">cutlass::reference::host::detail::TensorCopyIf::operator()</a></div><div class="ttdeci">void operator()(Coord&lt; DstLayout::kRank &gt; const &amp;coord)</div><div class="ttdoc">Copies based on destination and source bounds. </div><div class="ttdef"><b>Definition:</b> tensor_copy.h:93</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert_html"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html">cutlass::reference::host::detail::TrivialConvert</a></div><div class="ttdoc">Helper to convert between types. </div><div class="ttdef"><b>Definition:</b> tensor_copy.h:51</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_html_a9ee72419fd72488215e17ba746cc699d"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a9ee72419fd72488215e17ba746cc699d">cutlass::reference::host::detail::TensorCopyIf::TensorCopyIf</a></div><div class="ttdeci">TensorCopyIf(DstTensorView const &amp;dst_, SrcTensorView const &amp;src_, F const &amp;convert_)</div><div class="ttdef"><b>Definition:</b> tensor_copy.h:87</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html"><div class="ttname"><a href="classcutlass_1_1TensorView.html">cutlass::TensorView&lt; DstElement, DstLayout &gt;</a></div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html"><div class="ttname"><a href="classcutlass_1_1TensorRef.html">cutlass::TensorRef&lt; SrcElement, SrcLayout &gt;</a></div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_html"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html">cutlass::reference::host::detail::TensorCopyIf</a></div><div class="ttdoc">Helper to conditionally copy between tensor views. </div><div class="ttdef"><b>Definition:</b> tensor_copy.h:68</div></div>
<div class="ttc" id="structcutlass_1_1Coord_html"><div class="ttname"><a href="structcutlass_1_1Coord.html">cutlass::Coord</a></div><div class="ttdoc">Statically-sized array specifying Coords within a tensor. </div><div class="ttdef"><b>Definition:</b> coord.h:43</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_html_ad9e5902883076ca684487d276a79c47e"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#ad9e5902883076ca684487d276a79c47e">cutlass::reference::host::detail::TensorCopyIf::convert</a></div><div class="ttdeci">F convert</div><div class="ttdef"><b>Definition:</b> tensor_copy.h:79</div></div>
<div class="ttc" id="classcutlass_1_1TensorRef_html_a8758907a1c9b1fcd00e7ece626d03b76"><div class="ttname"><a href="classcutlass_1_1TensorRef.html#a8758907a1c9b1fcd00e7ece626d03b76">cutlass::TensorRef::at</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE Reference at(TensorCoord const &amp;coord) const </div><div class="ttdoc">Returns a reference to the element at a given Coord. </div><div class="ttdef"><b>Definition:</b> tensor_ref.h:307</div></div>
<div class="ttc" id="namespacecutlass_1_1reference_1_1host_html_a8c798c04df572b34e3ed3976d69f993d"><div class="ttname"><a href="namespacecutlass_1_1reference_1_1host.html#a8c798c04df572b34e3ed3976d69f993d">cutlass::reference::host::TensorForEach</a></div><div class="ttdeci">void TensorForEach(Coord&lt; Rank &gt; extent, Func &amp;func)</div><div class="ttdoc">Iterates over the index space of a tensor. </div><div class="ttdef"><b>Definition:</b> host/tensor_foreach.h:87</div></div>
<div class="ttc" id="classcutlass_1_1TensorView_html_a6a6a1f99d06abd8fb3f5a8e4e0fea25e"><div class="ttname"><a href="classcutlass_1_1TensorView.html#a6a6a1f99d06abd8fb3f5a8e4e0fea25e">cutlass::TensorView::contains</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE bool contains(TensorCoord const &amp;coord) const </div><div class="ttdoc">Determines whether a location is within a tensor. </div><div class="ttdef"><b>Definition:</b> tensor_view.h:175</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert_html_a329020d42d4ee1fdcdee82d70432d1ed"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TrivialConvert.html#a329020d42d4ee1fdcdee82d70432d1ed">cutlass::reference::host::detail::TrivialConvert::TrivialConvert</a></div><div class="ttdeci">TrivialConvert()</div><div class="ttdef"><b>Definition:</b> tensor_copy.h:53</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_html_a153ae0606432a65e3a4aa0017936181f"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a153ae0606432a65e3a4aa0017936181f">cutlass::reference::host::detail::TensorCopyIf::src</a></div><div class="ttdeci">SrcTensorView src</div><div class="ttdef"><b>Definition:</b> tensor_copy.h:78</div></div>
<div class="ttc" id="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf_html_a0d94963e36e238233ddb550845b37004"><div class="ttname"><a href="structcutlass_1_1reference_1_1host_1_1detail_1_1TensorCopyIf.html#a0d94963e36e238233ddb550845b37004">cutlass::reference::host::detail::TensorCopyIf::dst</a></div><div class="ttdeci">DstTensorView dst</div><div class="ttdef"><b>Definition:</b> tensor_copy.h:77</div></div>
<div class="ttc" id="cutlass_8h_html"><div class="ttname"><a href="cutlass_8h.html">cutlass.h</a></div><div class="ttdoc">Basic include for CUTLASS. </div></div>
<div class="ttc" id="host_2tensor__foreach_8h_html"><div class="ttname"><a href="host_2tensor__foreach_8h.html">tensor_foreach.h</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
