<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reduction::thread::ReduceAdd&lt; ElementAccumulator_, Element_, Count &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reduction.html">reduction</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reduction_1_1thread.html">thread</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">ReduceAdd</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reduction::thread::ReduceAdd&lt; ElementAccumulator_, Element_, Count &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Mixed-precision reduction.  
</p>

<p><code>#include &lt;<a class="el" href="reduction__operators_8h_source.html">reduction_operators.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reduction::thread::ReduceAdd&lt; ElementAccumulator_, Element_, Count &gt;:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd__coll__graph.png" border="0" usemap="#cutlass_1_1reduction_1_1thread_1_1ReduceAdd_3_01ElementAccumulator___00_01Element___00_01Count_01_4_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reduction_1_1thread_1_1ReduceAdd_3_01ElementAccumulator___00_01Element___00_01Count_01_4_coll__map" id="cutlass_1_1reduction_1_1thread_1_1ReduceAdd_3_01ElementAccumulator___00_01Element___00_01Count_01_4_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd_1_1Params.html">Params</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a74477c19b8ff1ea9699141531f99e4b0"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a74477c19b8ff1ea9699141531f99e4b0">ElementAccumulator</a> = ElementAccumulator_</td></tr>
<tr class="separator:a74477c19b8ff1ea9699141531f99e4b0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a775fb7b23de2bf82755da93980bbccdd"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a775fb7b23de2bf82755da93980bbccdd">Element</a> = Element_</td></tr>
<tr class="separator:a775fb7b23de2bf82755da93980bbccdd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab8b0d369480bff1792f6439c2bb0ba18"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ab8b0d369480bff1792f6439c2bb0ba18">FragmentAccumulator</a> = cutlass::Array&lt; <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a74477c19b8ff1ea9699141531f99e4b0">ElementAccumulator</a>, <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ad0d42ebeb2b4bbee843e5ee31a8c62c9">kCount</a> &gt;</td></tr>
<tr class="separator:ab8b0d369480bff1792f6439c2bb0ba18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a9cbf7165c8ca9da43b4038d7ea459a73"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a9cbf7165c8ca9da43b4038d7ea459a73">FragmentElement</a> = cutlass::Array&lt; <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a775fb7b23de2bf82755da93980bbccdd">Element</a>, <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ad0d42ebeb2b4bbee843e5ee31a8c62c9">kCount</a> &gt;</td></tr>
<tr class="separator:a9cbf7165c8ca9da43b4038d7ea459a73"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:ac9ccfca110d19133c4b203227577e2ec"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ac9ccfca110d19133c4b203227577e2ec">ReduceAdd</a> (<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd_1_1Params.html">Params</a> params_=<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd_1_1Params.html">Params</a>())</td></tr>
<tr class="memdesc:ac9ccfca110d19133c4b203227577e2ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Constructor.  <a href="#ac9ccfca110d19133c4b203227577e2ec">More...</a><br /></td></tr>
<tr class="separator:ac9ccfca110d19133c4b203227577e2ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac2fd8699c5a40b3c633fe8a59f02d8da"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ab8b0d369480bff1792f6439c2bb0ba18">FragmentAccumulator</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ac2fd8699c5a40b3c633fe8a59f02d8da">operator()</a> (<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ab8b0d369480bff1792f6439c2bb0ba18">FragmentAccumulator</a> accumulator, <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a9cbf7165c8ca9da43b4038d7ea459a73">FragmentElement</a> element) const </td></tr>
<tr class="memdesc:ac2fd8699c5a40b3c633fe8a59f02d8da"><td class="mdescLeft">&#160;</td><td class="mdescRight">Operator.  <a href="#ac2fd8699c5a40b3c633fe8a59f02d8da">More...</a><br /></td></tr>
<tr class="separator:ac2fd8699c5a40b3c633fe8a59f02d8da"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:ac3ba3575c91e948c1f622d068a181428"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd_1_1Params.html">Params</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ac3ba3575c91e948c1f622d068a181428">params</a></td></tr>
<tr class="memdesc:ac3ba3575c91e948c1f622d068a181428"><td class="mdescLeft">&#160;</td><td class="mdescRight">Parameters object.  <a href="#ac3ba3575c91e948c1f622d068a181428">More...</a><br /></td></tr>
<tr class="separator:ac3ba3575c91e948c1f622d068a181428"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:ad0d42ebeb2b4bbee843e5ee31a8c62c9"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ad0d42ebeb2b4bbee843e5ee31a8c62c9">kCount</a> = Count</td></tr>
<tr class="separator:ad0d42ebeb2b4bbee843e5ee31a8c62c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a775fb7b23de2bf82755da93980bbccdd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a775fb7b23de2bf82755da93980bbccdd">Element</a> =  Element_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a74477c19b8ff1ea9699141531f99e4b0"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a74477c19b8ff1ea9699141531f99e4b0">ElementAccumulator</a> =  ElementAccumulator_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab8b0d369480bff1792f6439c2bb0ba18"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ab8b0d369480bff1792f6439c2bb0ba18">FragmentAccumulator</a> =  cutlass::Array&lt;<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a74477c19b8ff1ea9699141531f99e4b0">ElementAccumulator</a>, <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ad0d42ebeb2b4bbee843e5ee31a8c62c9">kCount</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a9cbf7165c8ca9da43b4038d7ea459a73"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a9cbf7165c8ca9da43b4038d7ea459a73">FragmentElement</a> =  cutlass::Array&lt;<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a775fb7b23de2bf82755da93980bbccdd">Element</a>, <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ad0d42ebeb2b4bbee843e5ee31a8c62c9">kCount</a>&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="ac9ccfca110d19133c4b203227577e2ec"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::<a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">ReduceAdd</a> </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd_1_1Params.html">Params</a>&#160;</td>
          <td class="paramname"><em>params_</em> = <code><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd_1_1Params.html">Params</a>()</code></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="ac2fd8699c5a40b3c633fe8a59f02d8da"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ab8b0d369480bff1792f6439c2bb0ba18">FragmentAccumulator</a> <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::operator() </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#ab8b0d369480bff1792f6439c2bb0ba18">FragmentAccumulator</a>&#160;</td>
          <td class="paramname"><em>accumulator</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html#a9cbf7165c8ca9da43b4038d7ea459a73">FragmentElement</a>&#160;</td>
          <td class="paramname"><em>element</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td> const</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="ad0d42ebeb2b4bbee843e5ee31a8c62c9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::kCount = Count</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac3ba3575c91e948c1f622d068a181428"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ElementAccumulator_ , typename Element_ , int Count = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd_1_1Params.html">Params</a> <a class="el" href="structcutlass_1_1reduction_1_1thread_1_1ReduceAdd.html">cutlass::reduction::thread::ReduceAdd</a>&lt; ElementAccumulator_, Element_, Count &gt;::params</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="reduction__operators_8h_source.html">reduction_operators.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
