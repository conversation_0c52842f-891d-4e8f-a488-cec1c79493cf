<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: wmma.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_048c1df36ab9c2efbb0733edba6291c9.html">arch</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">wmma.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="wmma_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="comment">// CUTLASS WMMA does not support clang at present.</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#if !defined(__clang__)</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#if (__CUDACC_VER_MAJOR__ &gt;= 9)</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#if (!defined(__CUDA_ARCH__) || (__CUDA_ARCH__ &gt;= 700))</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#define CUTLASS_ARCH_WMMA_ENABLED</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#define CUTLASS_ARCH_WMMA_SM70_ENABLED</span></div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#if (__CUDACC_VER_MAJOR__ &gt;= 10)</span></div><div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#if (!defined(__CUDA_ARCH__) || (__CUDA_ARCH__ &gt;= 720))</span></div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#define CUTLASS_ARCH_INTEGER_MATRIX_MULTIPLY_ENABLED</span></div><div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#define CUTLASS_ARCH_WMMA_SM72_ENABLED</span></div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#if (__CUDACC_VER_MAJOR__ &gt;= 10)</span></div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">#if (!defined(__CUDA_ARCH__) || (__CUDA_ARCH__ &gt;= 750))</span></div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor">#define CUTLASS_SUBBYTE_INTEGER_MATRIX_MULTIPLY_ENABLED</span></div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">#define CUTLASS_ARCH_WMMA_SM75_ENABLED</span></div><div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">#endif //__clang__</span></div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="preprocessor">#if defined(CUTLASS_ARCH_WMMA_ENABLED)</span></div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;</div><div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;<span class="preprocessor">#include &lt;mma.h&gt;</span></div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="arch_2mma_8h.html">cutlass/arch/mma.h</a>&quot;</span></div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="array_8h.html">cutlass/array.h</a>&quot;</span></div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="include_2cutlass_2gemm_2gemm_8h.html">cutlass/gemm/gemm.h</a>&quot;</span></div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div><div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;<span class="keyword">namespace </span>arch {</div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="keyword">enum class</span> MemoryKind {</div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  kShared,  <span class="comment">// Data resides in shared memory</span></div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  kGlobal   <span class="comment">// Data resides in global memory</span></div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;};</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;</div><div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;<span class="keyword">struct </span>WarpParams {</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kThreadsPerWarp = 32;</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kQuadsPerWarp = 8;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kThreadsPerQuad = 4;</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;};</div><div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Type_&gt;</div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType{</div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;  <span class="keyword">using</span> Type = Type_;</div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;};</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType&lt;<a class="code" href="namespacecutlass.html">cutlass</a>::half_t&gt; {</div><div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;  <span class="keyword">using</span> Type = __half;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;};</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType&lt;int8_t&gt; {</div><div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;  <span class="keyword">using</span> Type = <span class="keywordtype">signed</span> char;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;};</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType&lt;uint8_t&gt; {</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="keyword">using</span> Type = <span class="keywordtype">unsigned</span> char;</div><div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;};</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType&lt;int32_t&gt; {</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;  <span class="keyword">using</span> Type = int;</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;};</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="preprocessor">#if defined(CUTLASS_SUBBYTE_INTEGER_MATRIX_MULTIPLY_ENABLED)</span></div><div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType&lt;<a class="code" href="namespacecutlass.html">cutlass</a>::<a class="code" href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">int4b_t</a>&gt; {</div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;  <span class="keyword">using</span> Type = nvcuda::wmma::experimental::precision::s4;</div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;};</div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType&lt;<a class="code" href="namespacecutlass.html">cutlass</a>::<a class="code" href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">uint4b_t</a>&gt; {</div><div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;  <span class="keyword">using</span> Type = nvcuda::wmma::experimental::precision::u4;</div><div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;};</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;<span class="keyword">struct </span>CutlassToWmmaDataType&lt;<a class="code" href="namespacecutlass.html">cutlass</a>::<a class="code" href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">uint1b_t</a>&gt; {</div><div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;  <span class="keyword">using</span> Type = nvcuda::wmma::experimental::precision::b1;</div><div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;};</div><div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div><div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Layout_&gt;</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;<span class="keyword">struct </span>CutlassToWmmaLayout {</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;};</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div><div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;<span class="keyword">struct </span>CutlassToWmmaLayout&lt;<a class="code" href="namespacecutlass.html">cutlass</a>::layout::RowMajor&gt; {</div><div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;  <span class="keyword">using</span> Layout = nvcuda::wmma::row_major;</div><div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;  <span class="keyword">static</span> nvcuda::wmma::layout_t <span class="keyword">const</span> value = nvcuda::wmma::layout_t::mem_row_major;</div><div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;};</div><div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;<span class="keyword">struct </span>CutlassToWmmaLayout&lt;<a class="code" href="namespacecutlass.html">cutlass</a>::layout::ColumnMajor&gt; {</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;  <span class="keyword">using</span> Layout = nvcuda::wmma::col_major;</div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;  <span class="keyword">static</span> nvcuda::wmma::layout_t <span class="keyword">const</span> value = nvcuda::wmma::layout_t::mem_col_major;</div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;};</div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;</div><div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> Type_&gt;</div><div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;<span class="keyword">struct </span>WmmaToCutlassDataType{</div><div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;  <span class="keyword">using</span> Type = Type_;</div><div class="line"><a name="l00172"></a><span class="lineno">  172</span>&#160;};</div><div class="line"><a name="l00173"></a><span class="lineno">  173</span>&#160;</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;<span class="keyword">template</span>&lt;&gt;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;<span class="keyword">struct </span>WmmaToCutlassDataType&lt;__half&gt; {</div><div class="line"><a name="l00177"></a><span class="lineno">  177</span>&#160;  <span class="keyword">using</span> Type = <a class="code" href="structcutlass_1_1half__t.html">cutlass::half_t</a>;</div><div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;};</div><div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;</div><div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;<span class="comment">// WMMA template structure defines nvcuda::wmma::fragments and static assertion chaeks</span></div><div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;<span class="comment">// for a specific template paramterized data type (Element[A|B|C]), layout (Layout[A|B|C]), </span></div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;<span class="comment">// and native wmma size (Shape)</span></div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;<span class="comment"></span><span class="keyword">template</span> &lt;  </div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;  <span class="keyword">typename</span> Shape_,                                   </div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;  <span class="keyword">typename</span> ElementA_,                                </div><div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;  <span class="keyword">typename</span> LayoutA_,                                 </div><div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;  <span class="keyword">typename</span> ElementB_,                                </div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;  <span class="keyword">typename</span> LayoutB_,                                 </div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;  <span class="keyword">typename</span> ElementC_,                                </div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;  <span class="keyword">typename</span> LayoutC_,                                 </div><div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;  <span class="keyword">typename</span> Operator_ = cutlass::arch::OpMultiplyAdd   </div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;&gt;</div><div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;<span class="keyword">struct </span>Wmma;</div><div class="line"><a name="l00198"></a><span class="lineno">  198</span>&#160;</div><div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;</div><div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;} <span class="comment">// namespace arch</span></div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00202"></a><span class="lineno">  202</span>&#160;</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;<span class="comment">// Specializations for each compute capability</span></div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;<span class="preprocessor">#ifdef CUTLASS_ARCH_WMMA_SM70_ENABLED</span></div><div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="wmma__sm70_8h.html">cutlass/arch/wmma_sm70.h</a>&quot;</span></div><div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;<span class="preprocessor">#ifdef CUTLASS_ARCH_WMMA_SM72_ENABLED</span></div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="wmma__sm72_8h.html">cutlass/arch/wmma_sm72.h</a>&quot;</span></div><div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;</div><div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;<span class="preprocessor">#ifdef CUTLASS_ARCH_WMMA_SM75_ENABLED</span></div><div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="wmma__sm75_8h.html">cutlass/arch/wmma_sm75.h</a>&quot;</span></div><div class="line"><a name="l00218"></a><span class="lineno">  218</span>&#160;<span class="preprocessor">#endif</span></div><div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;<span class="preprocessor">#endif //CUTLASS_ARCH_WMMA_ENABLED</span></div><div class="ttc" id="namespacecutlass_html_a674e7702adcb25f7d143d93926a2d61d"><div class="ttname"><a href="namespacecutlass.html#a674e7702adcb25f7d143d93926a2d61d">cutlass::uint4b_t</a></div><div class="ttdeci">integer_subbyte&lt; 4, false &gt; uint4b_t</div><div class="ttdoc">4-bit Unsigned integer type </div><div class="ttdef"><b>Definition:</b> integer_subbyte.h:158</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="wmma__sm75_8h_html"><div class="ttname"><a href="wmma__sm75_8h.html">wmma_sm75.h</a></div><div class="ttdoc">Matrix multiply. </div></div>
<div class="ttc" id="namespacecutlass_html_a09a3695d6126aed7a7c01f431fa34b7d"><div class="ttname"><a href="namespacecutlass.html#a09a3695d6126aed7a7c01f431fa34b7d">cutlass::uint1b_t</a></div><div class="ttdeci">integer_subbyte&lt; 1, false &gt; uint1b_t</div><div class="ttdoc">1-bit Unsigned integer type </div><div class="ttdef"><b>Definition:</b> integer_subbyte.h:152</div></div>
<div class="ttc" id="wmma__sm70_8h_html"><div class="ttname"><a href="wmma__sm70_8h.html">wmma_sm70.h</a></div><div class="ttdoc">Matrix multiply. </div></div>
<div class="ttc" id="structcutlass_1_1half__t_html"><div class="ttname"><a href="structcutlass_1_1half__t.html">cutlass::half_t</a></div><div class="ttdoc">IEEE half-precision floating-point type. </div><div class="ttdef"><b>Definition:</b> half.h:126</div></div>
<div class="ttc" id="include_2cutlass_2gemm_2gemm_8h_html"><div class="ttname"><a href="include_2cutlass_2gemm_2gemm_8h.html">gemm.h</a></div><div class="ttdoc">Defines common types used for all GEMM-like operators. </div></div>
<div class="ttc" id="wmma__sm72_8h_html"><div class="ttname"><a href="wmma__sm72_8h.html">wmma_sm72.h</a></div><div class="ttdoc">Matrix multiply. </div></div>
<div class="ttc" id="array_8h_html"><div class="ttname"><a href="array_8h.html">array.h</a></div><div class="ttdoc">Statically sized array of elements that accommodates all CUTLASS-supported numeric types and is safe ...</div></div>
<div class="ttc" id="arch_2mma_8h_html"><div class="ttname"><a href="arch_2mma_8h.html">mma.h</a></div><div class="ttdoc">Templates exposing architecture support for multiply-add operations. </div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="namespacecutlass_html_a30f409bb0c8a88a3307e5c7cd31f2384"><div class="ttname"><a href="namespacecutlass.html#a30f409bb0c8a88a3307e5c7cd31f2384">cutlass::int4b_t</a></div><div class="ttdeci">integer_subbyte&lt; 4, true &gt; int4b_t</div><div class="ttdoc">4-bit Integer type </div><div class="ttdef"><b>Definition:</b> integer_subbyte.h:155</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
