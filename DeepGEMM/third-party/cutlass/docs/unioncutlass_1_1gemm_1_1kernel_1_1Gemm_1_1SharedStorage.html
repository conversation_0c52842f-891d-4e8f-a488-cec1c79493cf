<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::gemm::kernel::Gemm&lt; Mma_, Epilogue_, ThreadblockSwizzle_, SplitKSerial &gt;::SharedStorage Union Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1gemm.html">gemm</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1gemm_1_1kernel.html">kernel</a></li><li class="navelem"><a class="el" href="structcutlass_1_1gemm_1_1kernel_1_1Gemm.html">Gemm</a></li><li class="navelem"><a class="el" href="unioncutlass_1_1gemm_1_1kernel_1_1Gemm_1_1SharedStorage.html">SharedStorage</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="unioncutlass_1_1gemm_1_1kernel_1_1Gemm_1_1SharedStorage-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::gemm::kernel::Gemm&lt; Mma_, Epilogue_, ThreadblockSwizzle_, SplitKSerial &gt;::SharedStorage Union Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Shared memory storage structure.  
</p>

<p><code>#include &lt;<a class="el" href="include_2cutlass_2gemm_2kernel_2gemm_8h_source.html">gemm.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a25ca6f379b42d97b73de07473e2fdf02"><td class="memItemLeft" align="right" valign="top">Mma::SharedStorage&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="unioncutlass_1_1gemm_1_1kernel_1_1Gemm_1_1SharedStorage.html#a25ca6f379b42d97b73de07473e2fdf02">main_loop</a></td></tr>
<tr class="separator:a25ca6f379b42d97b73de07473e2fdf02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aeed9542ff5c448269160ceb51fe2cf2b"><td class="memItemLeft" align="right" valign="top">Epilogue::SharedStorage&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="unioncutlass_1_1gemm_1_1kernel_1_1Gemm_1_1SharedStorage.html#aeed9542ff5c448269160ceb51fe2cf2b">epilogue</a></td></tr>
<tr class="separator:aeed9542ff5c448269160ceb51fe2cf2b"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="aeed9542ff5c448269160ceb51fe2cf2b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Mma_ , typename Epilogue_ , typename ThreadblockSwizzle_ , bool SplitKSerial&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">Epilogue::SharedStorage <a class="el" href="structcutlass_1_1gemm_1_1kernel_1_1Gemm.html">cutlass::gemm::kernel::Gemm</a>&lt; Mma_, Epilogue_, ThreadblockSwizzle_, SplitKSerial &gt;::SharedStorage::epilogue</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a25ca6f379b42d97b73de07473e2fdf02"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Mma_ , typename Epilogue_ , typename ThreadblockSwizzle_ , bool SplitKSerial&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">Mma::SharedStorage <a class="el" href="structcutlass_1_1gemm_1_1kernel_1_1Gemm.html">cutlass::gemm::kernel::Gemm</a>&lt; Mma_, Epilogue_, ThreadblockSwizzle_, SplitKSerial &gt;::SharedStorage::main_loop</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this union was generated from the following file:<ul>
<li><a class="el" href="include_2cutlass_2gemm_2kernel_2gemm_8h_source.html">include/cutlass/gemm/kernel/gemm.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
