<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: tensor_op_policy.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_d44c64559bbebec7f509842c48db8b23.html">include</a></li><li class="navelem"><a class="el" href="dir_6baf2bb612a2f0daa69af3101ede80a1.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_d9e7e9e63637345b8b26a82972709306.html">epilogue</a></li><li class="navelem"><a class="el" href="dir_e7fd38dbfb1fb5decd4aa6571e13ec6b.html">warp</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">tensor_op_policy.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="tensor__op__policy_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="matrix__shape_8h.html">cutlass/matrix_shape.h</a>&quot;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="layout_2matrix_8h.html">cutlass/layout/matrix.h</a>&quot;</span></div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">namespace </span>epilogue {</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="keyword">namespace </span>warp {</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;</div><div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div><div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;  <span class="keyword">typename</span> WarpShape,     </div><div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;  <span class="keyword">typename</span> OperatorShape, </div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;  <span class="keyword">typename</span> Layout         </div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;&gt;</div><div class="line"><a name="l00050"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">   50</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">TensorOpPolicy</a>; </div><div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div><div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;</div><div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;  <span class="keyword">typename</span> WarpShape,           </div><div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;  <span class="keyword">typename</span> OperatorShape        </div><div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;&gt;</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html">   59</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">TensorOpPolicy</a>&lt;WarpShape, OperatorShape, layout::RowMajor&gt; {</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">OperatorCount</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;</div><div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;    WarpShape::kM / OperatorShape::kM,</div><div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;    WarpShape::kN / OperatorShape::kN</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a3c05a42a7690fad66fce5869987732a7">   65</a></span>&#160;  &gt;;</div><div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div><div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;  <span class="comment">// Hard-coded constants regarding Tensor Operations</span></div><div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a118c88417d9e5be6e93cff0425080b7b">   71</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = 2;</div><div class="line"><a name="l00072"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#aa79c89ede52ba69379b93212cae62aee">   72</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerIteration = 8;</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;  <span class="comment">// Derived quantities</span></div><div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div><div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;  <span class="comment">// Number of &#39;externally visible&#39; iterations per actual instruction</span></div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a1525dbcf12c66f8b5413ebf7fc6fe5b7">   79</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterationsPerInstruction = OperatorShape::kM / kRowsPerIteration;</div><div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div><div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;  <span class="comment">// Number of externally visible iterations</span></div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#afc40fb3730419f5be0fa88c3bc399a72">   82</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = OperatorCount::kRow * kIterationsPerInstruction;</div><div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;</div><div class="line"><a name="l00084"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a27021254f64e4b95d6af6465e0622b06">   84</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccumulatorRowStride = kElementsPerAccess;</div><div class="line"><a name="l00085"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout_1_1RowMajor_01_4.html#a20499f0e2316d3699c077d28f7808f9a">   85</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kAccumulatorColumnStride = kElementsPerAccess * OperatorCount::kRow * kIterationsPerInstruction;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;};</div><div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;</div><div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div><div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;<span class="keyword">template</span> &lt;</div><div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;    <span class="keyword">typename</span> WarpShape,  </div><div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;    <span class="keyword">typename</span> OperatorShape,   </div><div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;    <span class="keywordtype">int</span> InterleavedK     </div><div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;    &gt;</div><div class="line"><a name="l00096"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html">   96</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">TensorOpPolicy</a>&lt;WarpShape, OperatorShape,</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;                      layout::ColumnMajorInterleaved&lt;InterleavedK&gt; &gt; {</div><div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;  <span class="keyword">using</span> <a class="code" href="structcutlass_1_1MatrixShape.html">OperatorCount</a> = <a class="code" href="structcutlass_1_1MatrixShape.html">MatrixShape</a>&lt;WarpShape::kM / OperatorShape::kM,</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a03ea2f7f5c133b1abd3f28f07fab8a1a">  100</a></span>&#160;                                    WarpShape::kN / OperatorShape::kN&gt;;</div><div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div><div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;  <span class="comment">// Hard-coded constants regarding Tensor Operations</span></div><div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div><div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a972aa0a9ed0e6c93cefe10f759d66846">  106</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kElementsPerAccess = 2;</div><div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a688fd87a9fd5f4760eeff8f54de08b0a">  107</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kRowsPerIteration = 8;</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;  <span class="comment">// Derived quantities</span></div><div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;  <span class="comment">//</span></div><div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;</div><div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;  <span class="comment">// Number of &#39;externally visible&#39; iterations per actual instruction</span></div><div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a3f83f7ea1229ddee9eb73e3b2b23c4d4">  114</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterationsPerInstruction =</div><div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;      OperatorShape::kM / kRowsPerIteration;</div><div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div><div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;  <span class="comment">// Number of externally visible iterations</span></div><div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_3_01WarpShape_00_01OperatorShape_00_01layout69549d10c3610d943987eb90e827bc05.html#a7f4b6a177dced958f52e8c2d05a7dbcd">  118</a></span>&#160;  <span class="keyword">static</span> <span class="keywordtype">int</span> <span class="keyword">const</span> kIterations = WarpShape::kN / InterleavedK *</div><div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;                                 OperatorCount::kRow *</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;                                 kIterationsPerInstruction;</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;};</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;</div><div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div><div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;} <span class="comment">// namespace warp</span></div><div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;} <span class="comment">// namespace epilogue</span></div><div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;} <span class="comment">// namespace cutlass</span></div><div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div><div class="ttc" id="structcutlass_1_1MatrixShape_html"><div class="ttname"><a href="structcutlass_1_1MatrixShape.html">cutlass::MatrixShape</a></div><div class="ttdoc">Describes the size of a matrix tile. </div><div class="ttdef"><b>Definition:</b> matrix_shape.h:42</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="matrix__shape_8h_html"><div class="ttname"><a href="matrix__shape_8h.html">matrix_shape.h</a></div><div class="ttdoc">Defines a Shape template for matrix tiles. </div></div>
<div class="ttc" id="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy_html"><div class="ttname"><a href="structcutlass_1_1epilogue_1_1warp_1_1TensorOpPolicy.html">cutlass::epilogue::warp::TensorOpPolicy</a></div><div class="ttdoc">Policy details related to the epilogue. </div><div class="ttdef"><b>Definition:</b> tensor_op_policy.h:50</div></div>
<div class="ttc" id="layout_2matrix_8h_html"><div class="ttname"><a href="layout_2matrix_8h.html">matrix.h</a></div><div class="ttdoc">Defines layout functions used by TensorRef and derived classes. </div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
