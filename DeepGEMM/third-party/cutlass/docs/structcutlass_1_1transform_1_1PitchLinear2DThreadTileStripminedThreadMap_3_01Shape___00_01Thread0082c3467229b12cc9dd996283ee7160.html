<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap&lt; Shape_, Threads, cutlass::layout::PitchLinearShape&lt; 4, 4 &gt; &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html">PitchLinear2DThreadTileStripminedThreadMap&lt; Shape_, Threads, cutlass::layout::PitchLinearShape&lt; 4, 4 &gt; &gt;</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread48bfab8a2d7359e0aa1522180ca66ba4.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap&lt; Shape_, Threads, cutlass::layout::PitchLinearShape&lt; 4, 4 &gt; &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread896c01a3c466da1bf392e0cdfced4d53.html">Detail</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal implementation details.  <a href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread896c01a3c466da1bf392e0cdfced4d53.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a3222173dc046712a091fe597e5b6b1b7"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a3222173dc046712a091fe597e5b6b1b7">TensorCoord</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td></tr>
<tr class="memdesc:a3222173dc046712a091fe597e5b6b1b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor coordinate.  <a href="#a3222173dc046712a091fe597e5b6b1b7">More...</a><br /></td></tr>
<tr class="separator:a3222173dc046712a091fe597e5b6b1b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a030d9f79c5f558e8d5f57458d40a8390"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a030d9f79c5f558e8d5f57458d40a8390">Shape</a> = Shape_</td></tr>
<tr class="memdesc:a030d9f79c5f558e8d5f57458d40a8390"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile shape.  <a href="#a030d9f79c5f558e8d5f57458d40a8390">More...</a><br /></td></tr>
<tr class="separator:a030d9f79c5f558e8d5f57458d40a8390"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af3fd7839e55e8db213926a49275a017d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#af3fd7839e55e8db213926a49275a017d">ThreadAccessShape</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt;</td></tr>
<tr class="memdesc:af3fd7839e55e8db213926a49275a017d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Access Shape of each thread.  <a href="#af3fd7839e55e8db213926a49275a017d">More...</a><br /></td></tr>
<tr class="separator:af3fd7839e55e8db213926a49275a017d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1ccecc2d9406a5d052b2ed15a74c1abe"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a1ccecc2d9406a5d052b2ed15a74c1abe">Iterations</a> = typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;=Detail::ShapeVec::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 1,(Threads &gt;=Detail::ShapeVec::kContiguous?Detail::ShapeVec::kStrided/(<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a>/Detail::ShapeVec::kContiguous):0) &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Detail::ShapeVec::kContiguous/<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a>, Detail::ShapeVec::kStrided &gt; &gt;::type</td></tr>
<tr class="memdesc:a1ccecc2d9406a5d052b2ed15a74c1abe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of iterations by each thread.  <a href="#a1ccecc2d9406a5d052b2ed15a74c1abe">More...</a><br /></td></tr>
<tr class="separator:a1ccecc2d9406a5d052b2ed15a74c1abe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abc06fa8bfb735fee7e8d89eee42842d7"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#abc06fa8bfb735fee7e8d89eee42842d7">Delta</a> = typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;=Detail::ShapeVec::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Shape::kContiguous, <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a> *ThreadAccessShape::kStrided/Detail::ShapeVec::kContiguous &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a> *ThreadAccessShape::kContiguous, 1 &gt; &gt;::type</td></tr>
<tr class="separator:abc06fa8bfb735fee7e8d89eee42842d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a96ac336cac3a8b4d10f1764a925e0902"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a3222173dc046712a091fe597e5b6b1b7">TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a96ac336cac3a8b4d10f1764a925e0902">initial_offset</a> (int thread_id)</td></tr>
<tr class="separator:a96ac336cac3a8b4d10f1764a925e0902"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:ad53906dfb9b5d6be913fd60f3b865751"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a> = Threads</td></tr>
<tr class="memdesc:ad53906dfb9b5d6be913fd60f3b865751"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads total.  <a href="#ad53906dfb9b5d6be913fd60f3b865751">More...</a><br /></td></tr>
<tr class="separator:ad53906dfb9b5d6be913fd60f3b865751"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a047e42641c9f61371d2b631e471f20e5"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a047e42641c9f61371d2b631e471f20e5">kElementsPerAccess</a> = ThreadAccessShape::kContiguous</td></tr>
<tr class="memdesc:a047e42641c9f61371d2b631e471f20e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract length of each access from Layout.  <a href="#a047e42641c9f61371d2b631e471f20e5">More...</a><br /></td></tr>
<tr class="separator:a047e42641c9f61371d2b631e471f20e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="abc06fa8bfb735fee7e8d89eee42842d7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#abc06fa8bfb735fee7e8d89eee42842d7">Delta</a> =  typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;= Detail::ShapeVec::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Shape::kContiguous, <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a> * ThreadAccessShape::kStrided / Detail::ShapeVec::kContiguous &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a> * ThreadAccessShape::kContiguous, 1 &gt; &gt;::type</td>
        </tr>
      </table>
</div><div class="memdoc">
<p>Interval between accesses along each dimension of the tensor's logical coordinate space (in units of Elements) </p>

</div>
</div>
<a class="anchor" id="a1ccecc2d9406a5d052b2ed15a74c1abe"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a1ccecc2d9406a5d052b2ed15a74c1abe">Iterations</a> =  typename <a class="el" href="structcutlass_1_1platform_1_1conditional.html">platform::conditional</a>&lt; Threads &gt;= Detail::ShapeVec::kContiguous, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; 1, (Threads &gt;= Detail::ShapeVec::kContiguous ? Detail::ShapeVec::kStrided / (<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a> / Detail::ShapeVec::kContiguous) : 0) &gt;, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Detail::ShapeVec::kContiguous / <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#ad53906dfb9b5d6be913fd60f3b865751">kThreads</a>, Detail::ShapeVec::kStrided &gt; &gt;::type</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a030d9f79c5f558e8d5f57458d40a8390"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a030d9f79c5f558e8d5f57458d40a8390">Shape</a> =  Shape_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3222173dc046712a091fe597e5b6b1b7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a3222173dc046712a091fe597e5b6b1b7">TensorCoord</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af3fd7839e55e8db213926a49275a017d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#af3fd7839e55e8db213926a49275a017d">ThreadAccessShape</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt;4, 4&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a96ac336cac3a8b4d10f1764a925e0902"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap_3_01Shape___00_01Thread0082c3467229b12cc9dd996283ee7160.html#a3222173dc046712a091fe597e5b6b1b7">TensorCoord</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::initial_offset </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>thread_id</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Maps thread ID to a coordinate offset within the tensor's logical coordinate space (in units of Elements) </p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a047e42641c9f61371d2b631e471f20e5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::kElementsPerAccess = ThreadAccessShape::kContiguous</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad53906dfb9b5d6be913fd60f3b865751"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinear2DThreadTileStripminedThreadMap.html">cutlass::transform::PitchLinear2DThreadTileStripminedThreadMap</a>&lt; Shape_, Threads, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">cutlass::layout::PitchLinearShape</a>&lt; 4, 4 &gt; &gt;::kThreads = Threads</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
