<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::TransposePitchLinearThreadMap2DThreadTile&lt; ThreadMap_ &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">TransposePitchLinearThreadMap2DThreadTile</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile&lt; ThreadMap_ &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p>Thread Mapping a 2D threadtiled mapping as a transposed Pitchlinear2DThreadTile mapping.
</p>

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:a4ddf46f2245841dd3af1c393bf52af2d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a4ddf46f2245841dd3af1c393bf52af2d">ThreadMap</a> = ThreadMap_</td></tr>
<tr class="memdesc:a4ddf46f2245841dd3af1c393bf52af2d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Underlying ThreadMap.  <a href="#a4ddf46f2245841dd3af1c393bf52af2d">More...</a><br /></td></tr>
<tr class="separator:a4ddf46f2245841dd3af1c393bf52af2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae6416ecb230cfb2008b97cdd539877d2"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#ae6416ecb230cfb2008b97cdd539877d2">TensorCoord</a> = typename ThreadMap::TensorCoord</td></tr>
<tr class="memdesc:ae6416ecb230cfb2008b97cdd539877d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor coordinate.  <a href="#ae6416ecb230cfb2008b97cdd539877d2">More...</a><br /></td></tr>
<tr class="separator:ae6416ecb230cfb2008b97cdd539877d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adaaa6ab3b60f1451c91f164d6a2284cb"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#adaaa6ab3b60f1451c91f164d6a2284cb">Shape</a> = typename ThreadMap::Shape</td></tr>
<tr class="memdesc:adaaa6ab3b60f1451c91f164d6a2284cb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile shape.  <a href="#adaaa6ab3b60f1451c91f164d6a2284cb">More...</a><br /></td></tr>
<tr class="separator:adaaa6ab3b60f1451c91f164d6a2284cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5efb60f867ee631dbd2b93042deeda7d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a5efb60f867ee631dbd2b93042deeda7d">Iterations</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; ThreadMap::Iterations::kStrided, ThreadMap::Iterations::kContiguous &gt;</td></tr>
<tr class="memdesc:a5efb60f867ee631dbd2b93042deeda7d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Iterations along each dimension (concept: PitchLinearShape)  <a href="#a5efb60f867ee631dbd2b93042deeda7d">More...</a><br /></td></tr>
<tr class="separator:a5efb60f867ee631dbd2b93042deeda7d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5b6b1eec828b0f9452fcf2bb6035b05d"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a5b6b1eec828b0f9452fcf2bb6035b05d">ThreadAccessShape</a> = typename ThreadMap::ThreadAccessShape</td></tr>
<tr class="memdesc:a5b6b1eec828b0f9452fcf2bb6035b05d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Delta betweeen accesses (units of elements, concept: PitchLinearShape)  <a href="#a5b6b1eec828b0f9452fcf2bb6035b05d">More...</a><br /></td></tr>
<tr class="separator:a5b6b1eec828b0f9452fcf2bb6035b05d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a674092700284a0aec2665e7ea118a024"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a674092700284a0aec2665e7ea118a024">Delta</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; ThreadMap::Delta::kStrided, ThreadMap::Delta::kContiguous &gt;</td></tr>
<tr class="separator:a674092700284a0aec2665e7ea118a024"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a93bf84427a6f28f45df317200ee2a404"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#ae6416ecb230cfb2008b97cdd539877d2">TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a93bf84427a6f28f45df317200ee2a404">initial_offset</a> (int thread_id)</td></tr>
<tr class="separator:a93bf84427a6f28f45df317200ee2a404"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a5245459784b7b74a864cf676dfe94228"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a5245459784b7b74a864cf676dfe94228">kThreads</a> = ThreadMap::kThreads</td></tr>
<tr class="memdesc:a5245459784b7b74a864cf676dfe94228"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads total.  <a href="#a5245459784b7b74a864cf676dfe94228">More...</a><br /></td></tr>
<tr class="separator:a5245459784b7b74a864cf676dfe94228"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a90624d1f686da60922f9bd7ddc6df5b7"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a90624d1f686da60922f9bd7ddc6df5b7">kElementsPerAccess</a> = ThreadMap::kElementsPerAccess</td></tr>
<tr class="memdesc:a90624d1f686da60922f9bd7ddc6df5b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract vector length from Layout.  <a href="#a90624d1f686da60922f9bd7ddc6df5b7">More...</a><br /></td></tr>
<tr class="separator:a90624d1f686da60922f9bd7ddc6df5b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a674092700284a0aec2665e7ea118a024"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a674092700284a0aec2665e7ea118a024">Delta</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;ThreadMap::Delta::kStrided, ThreadMap::Delta::kContiguous&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5efb60f867ee631dbd2b93042deeda7d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a5efb60f867ee631dbd2b93042deeda7d">Iterations</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;ThreadMap::Iterations::kStrided, ThreadMap::Iterations::kContiguous&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adaaa6ab3b60f1451c91f164d6a2284cb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#adaaa6ab3b60f1451c91f164d6a2284cb">Shape</a> =  typename ThreadMap::Shape</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae6416ecb230cfb2008b97cdd539877d2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#ae6416ecb230cfb2008b97cdd539877d2">TensorCoord</a> =  typename ThreadMap::TensorCoord</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5b6b1eec828b0f9452fcf2bb6035b05d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a5b6b1eec828b0f9452fcf2bb6035b05d">ThreadAccessShape</a> =  typename ThreadMap::ThreadAccessShape</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4ddf46f2245841dd3af1c393bf52af2d"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::<a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#a4ddf46f2245841dd3af1c393bf52af2d">ThreadMap</a> =  ThreadMap_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a93bf84427a6f28f45df317200ee2a404"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html#ae6416ecb230cfb2008b97cdd539877d2">TensorCoord</a> <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::initial_offset </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>thread_id</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Maps thread ID to a coordinate offset within the tensor's logical coordinate space Note this is slightly different from the one of <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">PitchLinearWarpRakedThreadMap</a>. </p>

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a90624d1f686da60922f9bd7ddc6df5b7"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::kElementsPerAccess = ThreadMap::kElementsPerAccess</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a5245459784b7b74a864cf676dfe94228"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ThreadMap_ &gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1TransposePitchLinearThreadMap2DThreadTile.html">cutlass::transform::TransposePitchLinearThreadMap2DThreadTile</a>&lt; ThreadMap_ &gt;::kThreads = ThreadMap::kThreads</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
