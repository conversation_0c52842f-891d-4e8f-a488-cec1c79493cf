<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: type_traits.h Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
      <li><a href="globals.html"><span>File&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_4eeb864c4eec08c7d6b9d3b0352cfdde.html">tools</a></li><li class="navelem"><a class="el" href="dir_88de82f9e8d739a2f42f92d95f0d7933.html">util</a></li><li class="navelem"><a class="el" href="dir_7e9e609009df72bf6226de354e72c328.html">include</a></li><li class="navelem"><a class="el" href="dir_ade2f6ff57439d30f4164e14e54bcf30.html">cutlass</a></li><li class="navelem"><a class="el" href="dir_ff60863f958a43c892071bb1f8a4c81a.html">util</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">type_traits.h</div>  </div>
</div><!--header-->
<div class="contents">
<a href="type__traits_8h.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;<span class="comment">/***************************************************************************************************</span></div><div class="line"><a name="l00002"></a><span class="lineno">    2</span>&#160;<span class="comment"> * Copyright (c) 2017-2019, NVIDIA CORPORATION.  All rights reserved.</span></div><div class="line"><a name="l00003"></a><span class="lineno">    3</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="comment"> * Redistribution and use in source and binary forms, with or without modification, are permitted</span></div><div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;<span class="comment"> * provided that the following conditions are met:</span></div><div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="comment"> *     * Redistributions of source code must retain the above copyright notice, this list of</span></div><div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="comment"> *       conditions and the following disclaimer.</span></div><div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="comment"> *     * Redistributions in binary form must reproduce the above copyright notice, this list of</span></div><div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="comment"> *       conditions and the following disclaimer in the documentation and/or other materials</span></div><div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;<span class="comment"> *       provided with the distribution.</span></div><div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="comment"> *     * Neither the name of the NVIDIA CORPORATION nor the names of its contributors may be used</span></div><div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;<span class="comment"> *       to endorse or promote products derived from this software without specific prior written</span></div><div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;<span class="comment"> *       permission.</span></div><div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;<span class="comment"> * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot; AND ANY EXPRESS OR</span></div><div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment"> * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND</span></div><div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="comment"> * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL NVIDIA CORPORATION BE LIABLE</span></div><div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="comment"> * FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,</span></div><div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;<span class="comment"> * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;</span></div><div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="comment"> * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,</span></div><div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="comment"> * STRICT LIABILITY, OR TOR (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE</span></div><div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="comment"> * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></div><div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="comment"> *</span></div><div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="comment"> **************************************************************************************************/</span></div><div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;<span class="preprocessor">#pragma once</span></div><div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div><div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;<span class="preprocessor">#include &lt;cublas_v2.h&gt;</span></div><div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;<span class="preprocessor">#include &lt;cuda_fp16.h&gt;</span></div><div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;<span class="preprocessor">#include &lt;stdint.h&gt;</span></div><div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;</div><div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="numeric__types_8h.html">cutlass/numeric_types.h</a>&quot;</span></div><div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="complex_8h.html">cutlass/complex.h</a>&quot;</span></div><div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;</div><div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="keyword">namespace </span><a class="code" href="namespacecutlass.html">cutlass</a> {</div><div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="keyword">struct </span>half_t;</div><div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div><div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;</div><div class="line"><a name="l00042"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits.html">   42</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a> {</div><div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits.html#a46c611219c13251a56122b385c096b83">   43</a></span>&#160;  <span class="keyword">typedef</span> T <a class="code" href="structcutlass_1_1TypeTraits.html#a46c611219c13251a56122b385c096b83">host_type</a>;</div><div class="line"><a name="l00044"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits.html#a0fff5d43bdc223aab64e32dd045e6c4c">   44</a></span>&#160;  <span class="keyword">typedef</span> T <a class="code" href="structcutlass_1_1TypeTraits.html#a0fff5d43bdc223aab64e32dd045e6c4c">device_type</a>;</div><div class="line"><a name="l00045"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits.html#acdf0f64b79e6b8c550e85376232723a8">   45</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> T <a class="code" href="structcutlass_1_1TypeTraits.html#acdf0f64b79e6b8c550e85376232723a8">remove_negative_zero</a>(T x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00046"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits.html#aefa56c124caff14cbee72476bc1c0ab2">   46</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> T <a class="code" href="structcutlass_1_1TypeTraits.html#aefa56c124caff14cbee72476bc1c0ab2">to_print</a>(T x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits.html#a4311be016f580599b361291b02f7e5f1">   47</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits.html#a4311be016f580599b361291b02f7e5f1">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;};</div><div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div><div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html">   51</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;int8_t&gt; {</div><div class="line"><a name="l00052"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#ac801fb97ec8a1a8cce0dbab46a614eff">   52</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_8I;</div><div class="line"><a name="l00053"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a97f22781b0e3895fb981693e20f915d6">   53</a></span>&#160;  <span class="keyword">typedef</span> int8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a97f22781b0e3895fb981693e20f915d6">host_type</a>;</div><div class="line"><a name="l00054"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a3569c760289b932f6acebffe42a1ff92">   54</a></span>&#160;  <span class="keyword">typedef</span> int8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a3569c760289b932f6acebffe42a1ff92">device_type</a>;</div><div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#aafce09049d9cd82c79aab4e652e5043b">   55</a></span>&#160;  <span class="keyword">typedef</span> int8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#aafce09049d9cd82c79aab4e652e5043b">integer_type</a>;</div><div class="line"><a name="l00056"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a7b02bc71285f43b9442fbd23be3323f5">   56</a></span>&#160;  <span class="keyword">typedef</span> uint8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a7b02bc71285f43b9442fbd23be3323f5">unsigned_type</a>;</div><div class="line"><a name="l00057"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#acb1301812bda85b78dde745889aa0d98">   57</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> int8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#acb1301812bda85b78dde745889aa0d98">remove_negative_zero</a>(int8_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00058"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a2b1e2ad97871de651d54035f97bb1149">   58</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a2b1e2ad97871de651d54035f97bb1149">to_print</a>(int8_t x) { <span class="keywordflow">return</span> (<span class="keywordtype">int</span>)x; }</div><div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a8ed63daf2822140e6f4820721d353112">   59</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a8ed63daf2822140e6f4820721d353112">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;};</div><div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div><div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html">   63</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;uint8_t&gt; {</div><div class="line"><a name="l00064"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#ae5edc866e5de8527b6ddf06c3844684b">   64</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_8I;</div><div class="line"><a name="l00065"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a38489c4605f497c7d7d8b766935805ed">   65</a></span>&#160;  <span class="keyword">typedef</span> uint8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a38489c4605f497c7d7d8b766935805ed">host_type</a>;</div><div class="line"><a name="l00066"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a582965751efd761e874611a3282dbe34">   66</a></span>&#160;  <span class="keyword">typedef</span> uint8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a582965751efd761e874611a3282dbe34">device_type</a>;</div><div class="line"><a name="l00067"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#ac014e1dc998a6bbd4fcc30f4544ce2be">   67</a></span>&#160;  <span class="keyword">typedef</span> uint8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#ac014e1dc998a6bbd4fcc30f4544ce2be">integer_type</a>;</div><div class="line"><a name="l00068"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a3ad36f02c21dbf06d3631793b524b5b7">   68</a></span>&#160;  <span class="keyword">typedef</span> uint8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a3ad36f02c21dbf06d3631793b524b5b7">unsigned_type</a>;</div><div class="line"><a name="l00069"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a11a6931adbdd24743a919c999f44eda9">   69</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> uint8_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a11a6931adbdd24743a919c999f44eda9">remove_negative_zero</a>(uint8_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00070"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a1d8e72e3085df6766bbdfdc83405f3a2">   70</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> uint32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a1d8e72e3085df6766bbdfdc83405f3a2">to_print</a>(uint8_t x) { <span class="keywordflow">return</span> (uint32_t)x; }</div><div class="line"><a name="l00071"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a84de624a6a2a3431cd4f842994a9946d">   71</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a84de624a6a2a3431cd4f842994a9946d">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;};</div><div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div><div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00075"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html">   75</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;int&gt; {</div><div class="line"><a name="l00076"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#abe5b201de5b1ef7a4e23f5ab6ed06f4a">   76</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_32I;</div><div class="line"><a name="l00077"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#adf3d967f203fd5010c88d02a90ff80f4">   77</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#adf3d967f203fd5010c88d02a90ff80f4">host_type</a>;</div><div class="line"><a name="l00078"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a03dd152e9eff97c5b378fbdd7fc6abb5">   78</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a03dd152e9eff97c5b378fbdd7fc6abb5">device_type</a>;</div><div class="line"><a name="l00079"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a5cdc27bfac5d2e9cc6173af77e492466">   79</a></span>&#160;  <span class="keyword">typedef</span> int32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a5cdc27bfac5d2e9cc6173af77e492466">integer_type</a>;</div><div class="line"><a name="l00080"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#aa905e1bad1fb7364a697000def9640d9">   80</a></span>&#160;  <span class="keyword">typedef</span> uint32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#aa905e1bad1fb7364a697000def9640d9">unsigned_type</a>;</div><div class="line"><a name="l00081"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a4fec4afa5e9b0aa26b7026e4345d1549">   81</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> int32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a4fec4afa5e9b0aa26b7026e4345d1549">remove_negative_zero</a>(int32_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00082"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#ab2f720b4fe5be6f94e140f61e3a90bb6">   82</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <span class="keywordtype">int</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#ab2f720b4fe5be6f94e140f61e3a90bb6">to_print</a>(<span class="keywordtype">int</span> x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00083"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a52c7501e863bca0d8ef933d9262663f1">   83</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a52c7501e863bca0d8ef933d9262663f1">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;};</div><div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div><div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00087"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html">   87</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;unsigned&gt; {</div><div class="line"><a name="l00088"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#aeafbc657f1a9020e36bbe523a33990b5">   88</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_32I;</div><div class="line"><a name="l00089"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ad829a8b3d5aa22ad4777d31fd32fbbf0">   89</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ad829a8b3d5aa22ad4777d31fd32fbbf0">host_type</a>;</div><div class="line"><a name="l00090"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#aa303609d2c6f1618d9360190f3e450a8">   90</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">unsigned</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#aa303609d2c6f1618d9360190f3e450a8">device_type</a>;</div><div class="line"><a name="l00091"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ad5047bae9ee9464f636be5269237185b">   91</a></span>&#160;  <span class="keyword">typedef</span> uint32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ad5047bae9ee9464f636be5269237185b">integer_type</a>;</div><div class="line"><a name="l00092"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#afec24cb83aee1027105d93e6785b6c3f">   92</a></span>&#160;  <span class="keyword">typedef</span> uint32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#afec24cb83aee1027105d93e6785b6c3f">unsigned_type</a>;</div><div class="line"><a name="l00093"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ac596d039114e2c2618da81c0076faa7e">   93</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> uint32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ac596d039114e2c2618da81c0076faa7e">remove_negative_zero</a>(uint32_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00094"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#a74cd051acce6d2394996d3184b061a33">   94</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> uint32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#a74cd051acce6d2394996d3184b061a33">to_print</a>(uint32_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00095"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#a2e7a848039a582d94742bd401653e8ab">   95</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#a2e7a848039a582d94742bd401653e8ab">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;};</div><div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div><div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00099"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html">   99</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;int64_t&gt; {</div><div class="line"><a name="l00100"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a24cf2f6d484f30a1b329c3f8c1fb573d">  100</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_8I;</div><div class="line"><a name="l00101"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a40e3692627a4d7697c573f0e90856ff2">  101</a></span>&#160;  <span class="keyword">typedef</span> int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a40e3692627a4d7697c573f0e90856ff2">host_type</a>;</div><div class="line"><a name="l00102"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ad67f4b5261dd7d128c97c74b86a55cc8">  102</a></span>&#160;  <span class="keyword">typedef</span> int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ad67f4b5261dd7d128c97c74b86a55cc8">device_type</a>;</div><div class="line"><a name="l00103"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#afa3be2c14e04b3b5d700f3e7400acc9f">  103</a></span>&#160;  <span class="keyword">typedef</span> int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#afa3be2c14e04b3b5d700f3e7400acc9f">integer_type</a>;</div><div class="line"><a name="l00104"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a09e7639fc05bcc2495d4e55d370532f5">  104</a></span>&#160;  <span class="keyword">typedef</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a09e7639fc05bcc2495d4e55d370532f5">unsigned_type</a>;</div><div class="line"><a name="l00105"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a5a3dbcca8e7766783c0bb55a20e3a822">  105</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a5a3dbcca8e7766783c0bb55a20e3a822">remove_negative_zero</a>(int64_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00106"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ac2e3e80e69399cc9e280dd56d9b457d0">  106</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ac2e3e80e69399cc9e280dd56d9b457d0">to_print</a>(int64_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00107"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ac334c6173456ab73fad04039b63d4f81">  107</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ac334c6173456ab73fad04039b63d4f81">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;};</div><div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;</div><div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00111"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html">  111</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;uint64_t&gt; {</div><div class="line"><a name="l00112"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a9ef28cd1f430f25cdda594f060f4e718">  112</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_8I;</div><div class="line"><a name="l00113"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#aa876cb0da0b4537d2a8af97db58c98b8">  113</a></span>&#160;  <span class="keyword">typedef</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#aa876cb0da0b4537d2a8af97db58c98b8">host_type</a>;</div><div class="line"><a name="l00114"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a9bc248b7cd871ca79a73e3321ef5c1d0">  114</a></span>&#160;  <span class="keyword">typedef</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a9bc248b7cd871ca79a73e3321ef5c1d0">device_type</a>;</div><div class="line"><a name="l00115"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#aca3cc5f4bfd6cdfc3b032c0f4a190b80">  115</a></span>&#160;  <span class="keyword">typedef</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#aca3cc5f4bfd6cdfc3b032c0f4a190b80">integer_type</a>;</div><div class="line"><a name="l00116"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a7cdac983133ee3621cb4d24a5c535828">  116</a></span>&#160;  <span class="keyword">typedef</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a7cdac983133ee3621cb4d24a5c535828">unsigned_type</a>;</div><div class="line"><a name="l00117"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#afeda28af3151a5de60ecb0de5b525dd1">  117</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#afeda28af3151a5de60ecb0de5b525dd1">remove_negative_zero</a>(uint64_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00118"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#adbfa43b34b8cfc394dc8d62db6766f75">  118</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#adbfa43b34b8cfc394dc8d62db6766f75">to_print</a>(uint64_t x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00119"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#ac3ec7169904cd50b939ababe12461244">  119</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#ac3ec7169904cd50b939ababe12461244">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;};</div><div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div><div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00123"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html">  123</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>&gt; {</div><div class="line"><a name="l00124"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a0491882d302a1038f1bb3c3d09374bb4">  124</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_16F;</div><div class="line"><a name="l00125"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a6ede104173852286f2b52d7db06a47cb">  125</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a6ede104173852286f2b52d7db06a47cb">host_type</a>;</div><div class="line"><a name="l00126"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#abdd84ca5cd2fe5209602fa730561a85c">  126</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#abdd84ca5cd2fe5209602fa730561a85c">device_type</a>;</div><div class="line"><a name="l00127"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a871eef488ed6c82367cc62fc044b8781">  127</a></span>&#160;  <span class="keyword">typedef</span> int16_t <a class="code" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a871eef488ed6c82367cc62fc044b8781">integer_type</a>;</div><div class="line"><a name="l00128"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#aa895a8a4ba16f312a2815809151a53f8">  128</a></span>&#160;  <span class="keyword">typedef</span> uint16_t <a class="code" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#aa895a8a4ba16f312a2815809151a53f8">unsigned_type</a>;</div><div class="line"><a name="l00129"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#afd4eed55e9957fabbf513c9556973e04">  129</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#afd4eed55e9957fabbf513c9556973e04">remove_negative_zero</a>(<a class="code" href="structcutlass_1_1half__t.html">half_t</a> x) {</div><div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;    <span class="keywordflow">return</span> (x.<a class="code" href="structcutlass_1_1half__t.html#aaee5cf278f88e1de09cbebf8cdd2cbe8">raw</a>() == 0x8000 ? <a class="code" href="structcutlass_1_1half__t.html#acb746c82bd4dd496f79b7e611e3653dd">half_t::bitcast</a>(0) : x);</div><div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;  }</div><div class="line"><a name="l00132"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a5f1ac1085f0c227da3a1b02c49321189">  132</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="structcutlass_1_1half__t.html">half_t</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a5f1ac1085f0c227da3a1b02c49321189">to_print</a>(<a class="code" href="structcutlass_1_1half__t.html">half_t</a> x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00133"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a41a25099cbe7be57b6cdd973a9ea088d">  133</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a41a25099cbe7be57b6cdd973a9ea088d">to_device</a>(<a class="code" href="structcutlass_1_1half__t.html">half_t</a> x) { <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>device_type <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(x); }</div><div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;};</div><div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;</div><div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00137"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html">  137</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;float&gt; {</div><div class="line"><a name="l00138"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#aa835af229fbe3c00ccc6ea164bb1eb62">  138</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_32F;</div><div class="line"><a name="l00139"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a9c54e89b3f094fb201b007367083c4b5">  139</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">float</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a9c54e89b3f094fb201b007367083c4b5">host_type</a>;</div><div class="line"><a name="l00140"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#aa4d3935a9d7fe380f6177020c9ef45be">  140</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">float</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#aa4d3935a9d7fe380f6177020c9ef45be">device_type</a>;</div><div class="line"><a name="l00141"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a571cc731a77f526150254679e8b4e9d3">  141</a></span>&#160;  <span class="keyword">typedef</span> int32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a571cc731a77f526150254679e8b4e9d3">integer_type</a>;</div><div class="line"><a name="l00142"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ab64442443c164c51701ba28af735f6e2">  142</a></span>&#160;  <span class="keyword">typedef</span> uint32_t <a class="code" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ab64442443c164c51701ba28af735f6e2">unsigned_type</a>;</div><div class="line"><a name="l00143"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a7a81197b153f223cf985e3fae743d437">  143</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <span class="keywordtype">float</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a7a81197b153f223cf985e3fae743d437">remove_negative_zero</a>(<span class="keywordtype">float</span> x) { <span class="keywordflow">return</span> x == -0.f ? 0.f : x; }</div><div class="line"><a name="l00144"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ad0fa008a2e7786120aa23ec11c605b90">  144</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <span class="keywordtype">float</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ad0fa008a2e7786120aa23ec11c605b90">to_print</a>(<span class="keywordtype">float</span> x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00145"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ac83b0379f0e78a9e6fb05b15302d21c4">  145</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ac83b0379f0e78a9e6fb05b15302d21c4">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;};</div><div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;</div><div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00149"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html">  149</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;double&gt; {</div><div class="line"><a name="l00150"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#ae0e23f7459fa1586160ae47e151428ae">  150</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_R_64F;</div><div class="line"><a name="l00151"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#abedd7daeba38be5c7c6d3b3753da85b5">  151</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#abedd7daeba38be5c7c6d3b3753da85b5">host_type</a>;</div><div class="line"><a name="l00152"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#abcb11e62d2ccc2fce7577ef63b2c1cf0">  152</a></span>&#160;  <span class="keyword">typedef</span> <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#abcb11e62d2ccc2fce7577ef63b2c1cf0">device_type</a>;</div><div class="line"><a name="l00153"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#aeb17f038584dc2584248fa79c2f99835">  153</a></span>&#160;  <span class="keyword">typedef</span> int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#aeb17f038584dc2584248fa79c2f99835">integer_type</a>;</div><div class="line"><a name="l00154"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#ace0532ebb98ae9afc75f32ba0137374f">  154</a></span>&#160;  <span class="keyword">typedef</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#ace0532ebb98ae9afc75f32ba0137374f">unsigned_type</a>;</div><div class="line"><a name="l00155"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#afa38cd9c83856851bf305e15e424b057">  155</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#afa38cd9c83856851bf305e15e424b057">remove_negative_zero</a>(<span class="keywordtype">double</span> x) { <span class="keywordflow">return</span> x == -0.0 ? 0.0 : x; }</div><div class="line"><a name="l00156"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#a0afc1ae6d0e94bac50ebec7cd053719e">  156</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <span class="keywordtype">double</span> <a class="code" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#a0afc1ae6d0e94bac50ebec7cd053719e">to_print</a>(<span class="keywordtype">double</span> x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00157"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#a7fbaf2396f9a2fb8392135012d113e02">  157</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01double_01_4.html#a7fbaf2396f9a2fb8392135012d113e02">to_device</a>(host_type x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;};</div><div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div><div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;<span class="comment">// Complex types</span></div><div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;<span class="comment">//</span></div><div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;<span class="comment"></span></div><div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00167"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html">  167</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;<a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;half&gt; &gt; {</div><div class="line"><a name="l00168"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#afe1a23ad5e158fc64fac88bd6095602e">  168</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_C_16F;</div><div class="line"><a name="l00169"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#ae7f5f02fd2b6307a8fcc3e0d146ec885">  169</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#ae7f5f02fd2b6307a8fcc3e0d146ec885">host_type</a>;</div><div class="line"><a name="l00170"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a01f6604dff64aedba173c1ceae9fd773">  170</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;half&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a01f6604dff64aedba173c1ceae9fd773">device_type</a>;</div><div class="line"><a name="l00171"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a7c4b7a2e3265c19a2ff7930fcf23db27">  171</a></span>&#160;  <span class="keyword">typedef</span> int16_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a7c4b7a2e3265c19a2ff7930fcf23db27">integer_type</a>;</div><div class="line"><a name="l00172"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a9144fe6ff4a1d2747eaf758a4a2c4e4e">  172</a></span>&#160;  <span class="keyword">typedef</span> uint16_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a9144fe6ff4a1d2747eaf758a4a2c4e4e">unsigned_type</a>;</div><div class="line"><a name="l00173"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#ab648e5aaa13ce327cea11b8b18bf2ae5">  173</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#ab648e5aaa13ce327cea11b8b18bf2ae5">to_device</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;half&gt;</a> x) { <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>device_type <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(x); }</div><div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;};</div><div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div><div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00177"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html">  177</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;<a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;<a class="code" href="structcutlass_1_1half__t.html">half_t</a>&gt; &gt; {</div><div class="line"><a name="l00178"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a5ca73eeea32d33e33e8a98890a78593d">  178</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_C_16F;</div><div class="line"><a name="l00179"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a2ccf6efccd8a576ca082ad34240fcaa2">  179</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a2ccf6efccd8a576ca082ad34240fcaa2">host_type</a>;</div><div class="line"><a name="l00180"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a7213cece86548037bcf7ad229633629d">  180</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;half&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a7213cece86548037bcf7ad229633629d">device_type</a>;</div><div class="line"><a name="l00181"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac936d3dbbc2cd53b42d6e97fcf0c350f">  181</a></span>&#160;  <span class="keyword">typedef</span> int16_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac936d3dbbc2cd53b42d6e97fcf0c350f">integer_type</a>;</div><div class="line"><a name="l00182"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a5e9a9520dd285a28f29371882e8199b9">  182</a></span>&#160;  <span class="keyword">typedef</span> uint16_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a5e9a9520dd285a28f29371882e8199b9">unsigned_type</a>;</div><div class="line"><a name="l00183"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac3cad0e8f771e936bfa2a64df50982dc">  183</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac3cad0e8f771e936bfa2a64df50982dc">remove_negative_zero</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a> x) {</div><div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a>(</div><div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;      <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>(x) == -0_hf ? 0_hf : <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>(x),</div><div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;      <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>(x) == -0_hf ? 0_hf : <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>(x)</div><div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;    );</div><div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;  }</div><div class="line"><a name="l00189"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a698ffc92b5a0717c0d3015b034cee4d3">  189</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a698ffc92b5a0717c0d3015b034cee4d3">to_print</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a> x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00190"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac6d25ea8ea7b1207a2ff6aff401ea249">  190</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac6d25ea8ea7b1207a2ff6aff401ea249">to_device</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;half_t&gt;</a> x) { <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>device_type <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(x); }</div><div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;};</div><div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div><div class="line"><a name="l00193"></a><span class="lineno">  193</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00194"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html">  194</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;<a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;float&gt; &gt; {</div><div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;</div><div class="line"><a name="l00196"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a6885f2871ac12091946d8f9a833efc0e">  196</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_C_32F;</div><div class="line"><a name="l00197"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a4e6d27a12f835434a16b0f7bc92ae0d2">  197</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a4e6d27a12f835434a16b0f7bc92ae0d2">host_type</a>;</div><div class="line"><a name="l00198"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a226f54f55c3a2154e89632ca81a99789">  198</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a226f54f55c3a2154e89632ca81a99789">device_type</a>;</div><div class="line"><a name="l00199"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a67120bdef9cad92ac289bf26fa8008d6">  199</a></span>&#160;  <span class="keyword">typedef</span> int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a67120bdef9cad92ac289bf26fa8008d6">integer_type</a>;</div><div class="line"><a name="l00200"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a35bedaea951cee4a763a5ddb6a1a2995">  200</a></span>&#160;  <span class="keyword">typedef</span> uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a35bedaea951cee4a763a5ddb6a1a2995">unsigned_type</a>;</div><div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div><div class="line"><a name="l00202"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a9c5132250bbcfbe3e7747277b875c6bf">  202</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a9c5132250bbcfbe3e7747277b875c6bf">remove_negative_zero</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a> x) {</div><div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a>(</div><div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;      <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>(x) == -0.f ? 0.f : <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>(x),</div><div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;      <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>(x) == -0.f ? 0.f : <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>(x)</div><div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;    );</div><div class="line"><a name="l00207"></a><span class="lineno">  207</span>&#160;  }</div><div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;</div><div class="line"><a name="l00209"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a41e52f63a778b0f71de346f685a62295">  209</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a41e52f63a778b0f71de346f685a62295">to_print</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a> x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00210"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#ae948b70522a237f498935a5864ebac7f">  210</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#ae948b70522a237f498935a5864ebac7f">to_device</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;float&gt;</a> x) { <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>device_type <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(x); }</div><div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;};</div><div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;</div><div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;<span class="keyword">template</span> &lt;&gt;</div><div class="line"><a name="l00214"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html">  214</a></span>&#160;<span class="keyword">struct </span><a class="code" href="structcutlass_1_1TypeTraits.html">TypeTraits</a>&lt;<a class="code" href="classcutlass_1_1complex.html">complex</a>&lt;double&gt; &gt; {</div><div class="line"><a name="l00215"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a474db90f9990e15f86a822e6a226eeb7">  215</a></span>&#160;  <span class="keyword">static</span> cudaDataType_t <span class="keyword">const</span> cublas_type = CUDA_C_64F;</div><div class="line"><a name="l00216"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a832a72bde8685698eaba0353b6b2d3af">  216</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a832a72bde8685698eaba0353b6b2d3af">host_type</a>;</div><div class="line"><a name="l00217"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a9aeac5291f73780ee4fd7c33966f56a3">  217</a></span>&#160;  <span class="keyword">typedef</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a9aeac5291f73780ee4fd7c33966f56a3">device_type</a>;</div><div class="line"><a name="l00218"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1integer__type.html#ad95c7b0fd5538d193b3581bf4cd7eca1">  218</a></span>&#160;  <span class="keyword">struct </span>integer_type { int64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1integer__type.html#ad95c7b0fd5538d193b3581bf4cd7eca1">real</a>, <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>; };</div><div class="line"><a name="l00219"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1unsigned__type.html#ab1d0b72c9376509deaf61bb2eed09dc5">  219</a></span>&#160;  <span class="keyword">struct </span>unsigned_type { uint64_t <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1unsigned__type.html#ab1d0b72c9376509deaf61bb2eed09dc5">real</a>, <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>; };</div><div class="line"><a name="l00220"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a116d619c589a5c64c57bbe01d880962a">  220</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a116d619c589a5c64c57bbe01d880962a">remove_negative_zero</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a> x) {</div><div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;    <span class="keywordflow">return</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a>(</div><div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;      <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>(x) == -0.0 ? 0.0 : <a class="code" href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">real</a>(x),</div><div class="line"><a name="l00223"></a><span class="lineno">  223</span>&#160;      <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>(x) == -0.0 ? 0.0 : <a class="code" href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">imag</a>(x)</div><div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;    );</div><div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;  }</div><div class="line"><a name="l00226"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#aba708b7caac2634255aea0b6890b445b">  226</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> <a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a> <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#aba708b7caac2634255aea0b6890b445b">to_print</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a> x) { <span class="keywordflow">return</span> x; }</div><div class="line"><a name="l00227"></a><span class="lineno"><a class="line" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#ae3ee811cbb4e6f58b2d6a642b92e7e39">  227</a></span>&#160;  <span class="keyword">static</span> <span class="keyword">inline</span> device_type <a class="code" href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#ae3ee811cbb4e6f58b2d6a642b92e7e39">to_device</a>(<a class="code" href="classcutlass_1_1complex.html">complex&lt;double&gt;</a> x) { <span class="keywordflow">return</span> <span class="keyword">reinterpret_cast&lt;</span>device_type <span class="keyword">const </span>&amp;<span class="keyword">&gt;</span>(x); }</div><div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;};</div><div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div><div class="line"><a name="l00231"></a><span class="lineno">  231</span>&#160;</div><div class="line"><a name="l00232"></a><span class="lineno">  232</span>&#160;}  <span class="comment">// namespace cutlass</span></div><div class="ttc" id="structcutlass_1_1TypeTraits_3_01int64__t_01_4_html_afa3be2c14e04b3b5d700f3e7400acc9f"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#afa3be2c14e04b3b5d700f3e7400acc9f">cutlass::TypeTraits&lt; int64_t &gt;::integer_type</a></div><div class="ttdeci">int64_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:103</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4_html_ae7f5f02fd2b6307a8fcc3e0d146ec885"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#ae7f5f02fd2b6307a8fcc3e0d146ec885">cutlass::TypeTraits&lt; complex&lt; half &gt; &gt;::host_type</a></div><div class="ttdeci">complex&lt; half_t &gt; host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:169</div></div>
<div class="ttc" id="structcutlass_1_1half__t_html_acb746c82bd4dd496f79b7e611e3653dd"><div class="ttname"><a href="structcutlass_1_1half__t.html#acb746c82bd4dd496f79b7e611e3653dd">cutlass::half_t::bitcast</a></div><div class="ttdeci">static CUTLASS_HOST_DEVICE half_t bitcast(uint16_t x)</div><div class="ttdoc">Constructs from an unsigned short. </div><div class="ttdef"><b>Definition:</b> half.h:141</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4_html_a41e52f63a778b0f71de346f685a62295"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a41e52f63a778b0f71de346f685a62295">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::to_print</a></div><div class="ttdeci">static complex&lt; float &gt; to_print(complex&lt; float &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:209</div></div>
<div class="ttc" id="namespacecutlass_html"><div class="ttname"><a href="namespacecutlass.html">cutlass</a></div><div class="ttdef"><b>Definition:</b> aligned_buffer.h:35</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01double_01_4_html_afa38cd9c83856851bf305e15e424b057"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01double_01_4.html#afa38cd9c83856851bf305e15e424b057">cutlass::TypeTraits&lt; double &gt;::remove_negative_zero</a></div><div class="ttdeci">static double remove_negative_zero(double x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:155</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_html_a46c611219c13251a56122b385c096b83"><div class="ttname"><a href="structcutlass_1_1TypeTraits.html#a46c611219c13251a56122b385c096b83">cutlass::TypeTraits::host_type</a></div><div class="ttdeci">T host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:43</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01half__t_01_4_html_a871eef488ed6c82367cc62fc044b8781"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a871eef488ed6c82367cc62fc044b8781">cutlass::TypeTraits&lt; half_t &gt;::integer_type</a></div><div class="ttdeci">int16_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:127</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01double_01_4_html_ace0532ebb98ae9afc75f32ba0137374f"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01double_01_4.html#ace0532ebb98ae9afc75f32ba0137374f">cutlass::TypeTraits&lt; double &gt;::unsigned_type</a></div><div class="ttdeci">uint64_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:154</div></div>
<div class="ttc" id="complex_8h_html"><div class="ttname"><a href="complex_8h.html">complex.h</a></div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01float_01_4_html_aa4d3935a9d7fe380f6177020c9ef45be"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01float_01_4.html#aa4d3935a9d7fe380f6177020c9ef45be">cutlass::TypeTraits&lt; float &gt;::device_type</a></div><div class="ttdeci">float device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:140</div></div>
<div class="ttc" id="namespacecutlass_html_a236d41e43fc97943fb2412fcbb40aec1"><div class="ttname"><a href="namespacecutlass.html#a236d41e43fc97943fb2412fcbb40aec1">cutlass::imag</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE float const &amp; imag(cuFloatComplex const &amp;z)</div><div class="ttdoc">Returns the imaginary part of the complex number. </div><div class="ttdef"><b>Definition:</b> complex.h:72</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int8__t_01_4_html_acb1301812bda85b78dde745889aa0d98"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#acb1301812bda85b78dde745889aa0d98">cutlass::TypeTraits&lt; int8_t &gt;::remove_negative_zero</a></div><div class="ttdeci">static int8_t remove_negative_zero(int8_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:57</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01float_01_4_html_a7a81197b153f223cf985e3fae743d437"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a7a81197b153f223cf985e3fae743d437">cutlass::TypeTraits&lt; float &gt;::remove_negative_zero</a></div><div class="ttdeci">static float remove_negative_zero(float x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:143</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01unsigned_01_4_html_a2e7a848039a582d94742bd401653e8ab"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#a2e7a848039a582d94742bd401653e8ab">cutlass::TypeTraits&lt; unsigned &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:95</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01unsigned_01_4_html_ad5047bae9ee9464f636be5269237185b"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ad5047bae9ee9464f636be5269237185b">cutlass::TypeTraits&lt; unsigned &gt;::integer_type</a></div><div class="ttdeci">uint32_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:91</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4_html_ac936d3dbbc2cd53b42d6e97fcf0c350f"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac936d3dbbc2cd53b42d6e97fcf0c350f">cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::integer_type</a></div><div class="ttdeci">int16_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:181</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_html_a0fff5d43bdc223aab64e32dd045e6c4c"><div class="ttname"><a href="structcutlass_1_1TypeTraits.html#a0fff5d43bdc223aab64e32dd045e6c4c">cutlass::TypeTraits::device_type</a></div><div class="ttdeci">T device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:44</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int_01_4_html_a5cdc27bfac5d2e9cc6173af77e492466"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a5cdc27bfac5d2e9cc6173af77e492466">cutlass::TypeTraits&lt; int &gt;::integer_type</a></div><div class="ttdeci">int32_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:79</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01unsigned_01_4_html_afec24cb83aee1027105d93e6785b6c3f"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#afec24cb83aee1027105d93e6785b6c3f">cutlass::TypeTraits&lt; unsigned &gt;::unsigned_type</a></div><div class="ttdeci">uint32_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:92</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int_01_4_html_a03dd152e9eff97c5b378fbdd7fc6abb5"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a03dd152e9eff97c5b378fbdd7fc6abb5">cutlass::TypeTraits&lt; int &gt;::device_type</a></div><div class="ttdeci">int device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:78</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int_01_4_html_a4fec4afa5e9b0aa26b7026e4345d1549"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a4fec4afa5e9b0aa26b7026e4345d1549">cutlass::TypeTraits&lt; int &gt;::remove_negative_zero</a></div><div class="ttdeci">static int32_t remove_negative_zero(int32_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:81</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01unsigned_01_4_html_ac596d039114e2c2618da81c0076faa7e"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ac596d039114e2c2618da81c0076faa7e">cutlass::TypeTraits&lt; unsigned &gt;::remove_negative_zero</a></div><div class="ttdeci">static uint32_t remove_negative_zero(uint32_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:93</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint64__t_01_4_html_ac3ec7169904cd50b939ababe12461244"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#ac3ec7169904cd50b939ababe12461244">cutlass::TypeTraits&lt; uint64_t &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:119</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int64__t_01_4_html_a09e7639fc05bcc2495d4e55d370532f5"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a09e7639fc05bcc2495d4e55d370532f5">cutlass::TypeTraits&lt; int64_t &gt;::unsigned_type</a></div><div class="ttdeci">uint64_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:104</div></div>
<div class="ttc" id="structcutlass_1_1half__t_html"><div class="ttname"><a href="structcutlass_1_1half__t.html">cutlass::half_t</a></div><div class="ttdoc">IEEE half-precision floating-point type. </div><div class="ttdef"><b>Definition:</b> half.h:126</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_html_a9aeac5291f73780ee4fd7c33966f56a3"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a9aeac5291f73780ee4fd7c33966f56a3">cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::device_type</a></div><div class="ttdeci">complex&lt; double &gt; device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:217</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4_html_a7c4b7a2e3265c19a2ff7930fcf23db27"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a7c4b7a2e3265c19a2ff7930fcf23db27">cutlass::TypeTraits&lt; complex&lt; half &gt; &gt;::integer_type</a></div><div class="ttdeci">int16_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:171</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint64__t_01_4_html_aca3cc5f4bfd6cdfc3b032c0f4a190b80"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#aca3cc5f4bfd6cdfc3b032c0f4a190b80">cutlass::TypeTraits&lt; uint64_t &gt;::integer_type</a></div><div class="ttdeci">uint64_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:115</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_html_aba708b7caac2634255aea0b6890b445b"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#aba708b7caac2634255aea0b6890b445b">cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::to_print</a></div><div class="ttdeci">static complex&lt; double &gt; to_print(complex&lt; double &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:226</div></div>
<div class="ttc" id="namespacecutlass_html_ac0ea92c9a2a594446a84f7f86a79e143"><div class="ttname"><a href="namespacecutlass.html#ac0ea92c9a2a594446a84f7f86a79e143">cutlass::real</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE float const &amp; real(cuFloatComplex const &amp;z)</div><div class="ttdoc">Returns the real part of the complex number. </div><div class="ttdef"><b>Definition:</b> complex.h:56</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01half__t_01_4_html_afd4eed55e9957fabbf513c9556973e04"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#afd4eed55e9957fabbf513c9556973e04">cutlass::TypeTraits&lt; half_t &gt;::remove_negative_zero</a></div><div class="ttdeci">static half_t remove_negative_zero(half_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:129</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4_html_a7213cece86548037bcf7ad229633629d"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a7213cece86548037bcf7ad229633629d">cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::device_type</a></div><div class="ttdeci">complex&lt; half &gt; device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:180</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint64__t_01_4_html_aa876cb0da0b4537d2a8af97db58c98b8"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#aa876cb0da0b4537d2a8af97db58c98b8">cutlass::TypeTraits&lt; uint64_t &gt;::host_type</a></div><div class="ttdeci">uint64_t host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:113</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01float_01_4_html_ac83b0379f0e78a9e6fb05b15302d21c4"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ac83b0379f0e78a9e6fb05b15302d21c4">cutlass::TypeTraits&lt; float &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:145</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int64__t_01_4_html_a40e3692627a4d7697c573f0e90856ff2"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a40e3692627a4d7697c573f0e90856ff2">cutlass::TypeTraits&lt; int64_t &gt;::host_type</a></div><div class="ttdeci">int64_t host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:101</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01half__t_01_4_html_a5f1ac1085f0c227da3a1b02c49321189"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a5f1ac1085f0c227da3a1b02c49321189">cutlass::TypeTraits&lt; half_t &gt;::to_print</a></div><div class="ttdeci">static half_t to_print(half_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:132</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint8__t_01_4_html_ac014e1dc998a6bbd4fcc30f4544ce2be"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#ac014e1dc998a6bbd4fcc30f4544ce2be">cutlass::TypeTraits&lt; uint8_t &gt;::integer_type</a></div><div class="ttdeci">uint8_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:67</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_html_acdf0f64b79e6b8c550e85376232723a8"><div class="ttname"><a href="structcutlass_1_1TypeTraits.html#acdf0f64b79e6b8c550e85376232723a8">cutlass::TypeTraits::remove_negative_zero</a></div><div class="ttdeci">static T remove_negative_zero(T x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:45</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01double_01_4_html_a7fbaf2396f9a2fb8392135012d113e02"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01double_01_4.html#a7fbaf2396f9a2fb8392135012d113e02">cutlass::TypeTraits&lt; double &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:157</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4_html_a2ccf6efccd8a576ca082ad34240fcaa2"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a2ccf6efccd8a576ca082ad34240fcaa2">cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::host_type</a></div><div class="ttdeci">complex&lt; half_t &gt; host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:179</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01float_01_4_html_ad0fa008a2e7786120aa23ec11c605b90"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ad0fa008a2e7786120aa23ec11c605b90">cutlass::TypeTraits&lt; float &gt;::to_print</a></div><div class="ttdeci">static float to_print(float x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:144</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int64__t_01_4_html_ad67f4b5261dd7d128c97c74b86a55cc8"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ad67f4b5261dd7d128c97c74b86a55cc8">cutlass::TypeTraits&lt; int64_t &gt;::device_type</a></div><div class="ttdeci">int64_t device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:102</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_html_a116d619c589a5c64c57bbe01d880962a"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a116d619c589a5c64c57bbe01d880962a">cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::remove_negative_zero</a></div><div class="ttdeci">static complex&lt; double &gt; remove_negative_zero(complex&lt; double &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:220</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint8__t_01_4_html_a11a6931adbdd24743a919c999f44eda9"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a11a6931adbdd24743a919c999f44eda9">cutlass::TypeTraits&lt; uint8_t &gt;::remove_negative_zero</a></div><div class="ttdeci">static uint8_t remove_negative_zero(uint8_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:69</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01double_01_4_html_abcb11e62d2ccc2fce7577ef63b2c1cf0"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01double_01_4.html#abcb11e62d2ccc2fce7577ef63b2c1cf0">cutlass::TypeTraits&lt; double &gt;::device_type</a></div><div class="ttdeci">double device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:152</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int64__t_01_4_html_a5a3dbcca8e7766783c0bb55a20e3a822"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#a5a3dbcca8e7766783c0bb55a20e3a822">cutlass::TypeTraits&lt; int64_t &gt;::remove_negative_zero</a></div><div class="ttdeci">static int64_t remove_negative_zero(int64_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:105</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint64__t_01_4_html_adbfa43b34b8cfc394dc8d62db6766f75"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#adbfa43b34b8cfc394dc8d62db6766f75">cutlass::TypeTraits&lt; uint64_t &gt;::to_print</a></div><div class="ttdeci">static uint64_t to_print(uint64_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:118</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4_html_a67120bdef9cad92ac289bf26fa8008d6"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a67120bdef9cad92ac289bf26fa8008d6">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::integer_type</a></div><div class="ttdeci">int64_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:199</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01half__t_01_4_html_a41a25099cbe7be57b6cdd973a9ea088d"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a41a25099cbe7be57b6cdd973a9ea088d">cutlass::TypeTraits&lt; half_t &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(half_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:133</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_html_a832a72bde8685698eaba0353b6b2d3af"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#a832a72bde8685698eaba0353b6b2d3af">cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::host_type</a></div><div class="ttdeci">complex&lt; double &gt; host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:216</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint64__t_01_4_html_afeda28af3151a5de60ecb0de5b525dd1"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#afeda28af3151a5de60ecb0de5b525dd1">cutlass::TypeTraits&lt; uint64_t &gt;::remove_negative_zero</a></div><div class="ttdeci">static uint64_t remove_negative_zero(uint64_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:117</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01float_01_4_html_ab64442443c164c51701ba28af735f6e2"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01float_01_4.html#ab64442443c164c51701ba28af735f6e2">cutlass::TypeTraits&lt; float &gt;::unsigned_type</a></div><div class="ttdeci">uint32_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:142</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01unsigned_01_4_html_a74cd051acce6d2394996d3184b061a33"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#a74cd051acce6d2394996d3184b061a33">cutlass::TypeTraits&lt; unsigned &gt;::to_print</a></div><div class="ttdeci">static uint32_t to_print(uint32_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:94</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01float_01_4_html_a571cc731a77f526150254679e8b4e9d3"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a571cc731a77f526150254679e8b4e9d3">cutlass::TypeTraits&lt; float &gt;::integer_type</a></div><div class="ttdeci">int32_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:141</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int_01_4_html_a52c7501e863bca0d8ef933d9262663f1"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int_01_4.html#a52c7501e863bca0d8ef933d9262663f1">cutlass::TypeTraits&lt; int &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:83</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int64__t_01_4_html_ac334c6173456ab73fad04039b63d4f81"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ac334c6173456ab73fad04039b63d4f81">cutlass::TypeTraits&lt; int64_t &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:107</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01double_01_4_html_abedd7daeba38be5c7c6d3b3753da85b5"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01double_01_4.html#abedd7daeba38be5c7c6d3b3753da85b5">cutlass::TypeTraits&lt; double &gt;::host_type</a></div><div class="ttdeci">double host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:151</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int8__t_01_4_html_a3569c760289b932f6acebffe42a1ff92"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a3569c760289b932f6acebffe42a1ff92">cutlass::TypeTraits&lt; int8_t &gt;::device_type</a></div><div class="ttdeci">int8_t device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:54</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int_01_4_html_aa905e1bad1fb7364a697000def9640d9"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int_01_4.html#aa905e1bad1fb7364a697000def9640d9">cutlass::TypeTraits&lt; int &gt;::unsigned_type</a></div><div class="ttdeci">uint32_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:80</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1unsigned__type_html_ab1d0b72c9376509deaf61bb2eed09dc5"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1unsigned__type.html#ab1d0b72c9376509deaf61bb2eed09dc5">cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::unsigned_type::real</a></div><div class="ttdeci">uint64_t real</div><div class="ttdef"><b>Definition:</b> type_traits.h:219</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4_html_ae948b70522a237f498935a5864ebac7f"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#ae948b70522a237f498935a5864ebac7f">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(complex&lt; float &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:210</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01unsigned_01_4_html_aa303609d2c6f1618d9360190f3e450a8"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#aa303609d2c6f1618d9360190f3e450a8">cutlass::TypeTraits&lt; unsigned &gt;::device_type</a></div><div class="ttdeci">unsigned device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:90</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint64__t_01_4_html_a9bc248b7cd871ca79a73e3321ef5c1d0"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a9bc248b7cd871ca79a73e3321ef5c1d0">cutlass::TypeTraits&lt; uint64_t &gt;::device_type</a></div><div class="ttdeci">uint64_t device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:114</div></div>
<div class="ttc" id="numeric__types_8h_html"><div class="ttname"><a href="numeric__types_8h.html">numeric_types.h</a></div><div class="ttdoc">Top-level include for all CUTLASS numeric types. </div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int64__t_01_4_html_ac2e3e80e69399cc9e280dd56d9b457d0"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int64__t_01_4.html#ac2e3e80e69399cc9e280dd56d9b457d0">cutlass::TypeTraits&lt; int64_t &gt;::to_print</a></div><div class="ttdeci">static int64_t to_print(int64_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:106</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int8__t_01_4_html_a7b02bc71285f43b9442fbd23be3323f5"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a7b02bc71285f43b9442fbd23be3323f5">cutlass::TypeTraits&lt; int8_t &gt;::unsigned_type</a></div><div class="ttdeci">uint8_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:56</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint8__t_01_4_html_a3ad36f02c21dbf06d3631793b524b5b7"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a3ad36f02c21dbf06d3631793b524b5b7">cutlass::TypeTraits&lt; uint8_t &gt;::unsigned_type</a></div><div class="ttdeci">uint8_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:68</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_html_a4311be016f580599b361291b02f7e5f1"><div class="ttname"><a href="structcutlass_1_1TypeTraits.html#a4311be016f580599b361291b02f7e5f1">cutlass::TypeTraits::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:47</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int8__t_01_4_html_aafce09049d9cd82c79aab4e652e5043b"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#aafce09049d9cd82c79aab4e652e5043b">cutlass::TypeTraits&lt; int8_t &gt;::integer_type</a></div><div class="ttdeci">int8_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:55</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int_01_4_html_ab2f720b4fe5be6f94e140f61e3a90bb6"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int_01_4.html#ab2f720b4fe5be6f94e140f61e3a90bb6">cutlass::TypeTraits&lt; int &gt;::to_print</a></div><div class="ttdeci">static int to_print(int x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:82</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1integer__type_html_ad95c7b0fd5538d193b3581bf4cd7eca1"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_1_1integer__type.html#ad95c7b0fd5538d193b3581bf4cd7eca1">cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::integer_type::real</a></div><div class="ttdeci">int64_t real</div><div class="ttdef"><b>Definition:</b> type_traits.h:218</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01half__t_01_4_html_aa895a8a4ba16f312a2815809151a53f8"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#aa895a8a4ba16f312a2815809151a53f8">cutlass::TypeTraits&lt; half_t &gt;::unsigned_type</a></div><div class="ttdeci">uint16_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:128</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint8__t_01_4_html_a1d8e72e3085df6766bbdfdc83405f3a2"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a1d8e72e3085df6766bbdfdc83405f3a2">cutlass::TypeTraits&lt; uint8_t &gt;::to_print</a></div><div class="ttdeci">static uint32_t to_print(uint8_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:70</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01double_01_4_html_a0afc1ae6d0e94bac50ebec7cd053719e"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01double_01_4.html#a0afc1ae6d0e94bac50ebec7cd053719e">cutlass::TypeTraits&lt; double &gt;::to_print</a></div><div class="ttdeci">static double to_print(double x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:156</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4_html_ac6d25ea8ea7b1207a2ff6aff401ea249"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac6d25ea8ea7b1207a2ff6aff401ea249">cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(complex&lt; half_t &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:190</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01unsigned_01_4_html_ad829a8b3d5aa22ad4777d31fd32fbbf0"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01unsigned_01_4.html#ad829a8b3d5aa22ad4777d31fd32fbbf0">cutlass::TypeTraits&lt; unsigned &gt;::host_type</a></div><div class="ttdeci">unsigned host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:89</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01half__t_01_4_html_a6ede104173852286f2b52d7db06a47cb"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#a6ede104173852286f2b52d7db06a47cb">cutlass::TypeTraits&lt; half_t &gt;::host_type</a></div><div class="ttdeci">half_t host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:125</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint64__t_01_4_html_a7cdac983133ee3621cb4d24a5c535828"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint64__t_01_4.html#a7cdac983133ee3621cb4d24a5c535828">cutlass::TypeTraits&lt; uint64_t &gt;::unsigned_type</a></div><div class="ttdeci">uint64_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:116</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint8__t_01_4_html_a38489c4605f497c7d7d8b766935805ed"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a38489c4605f497c7d7d8b766935805ed">cutlass::TypeTraits&lt; uint8_t &gt;::host_type</a></div><div class="ttdeci">uint8_t host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:65</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01double_01_4_html_aeb17f038584dc2584248fa79c2f99835"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01double_01_4.html#aeb17f038584dc2584248fa79c2f99835">cutlass::TypeTraits&lt; double &gt;::integer_type</a></div><div class="ttdeci">int64_t integer_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:153</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4_html_a9144fe6ff4a1d2747eaf758a4a2c4e4e"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a9144fe6ff4a1d2747eaf758a4a2c4e4e">cutlass::TypeTraits&lt; complex&lt; half &gt; &gt;::unsigned_type</a></div><div class="ttdeci">uint16_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:172</div></div>
<div class="ttc" id="classcutlass_1_1complex_html"><div class="ttname"><a href="classcutlass_1_1complex.html">cutlass::complex</a></div><div class="ttdef"><b>Definition:</b> complex.h:92</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4_html_a9c5132250bbcfbe3e7747277b875c6bf"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a9c5132250bbcfbe3e7747277b875c6bf">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::remove_negative_zero</a></div><div class="ttdeci">static complex&lt; float &gt; remove_negative_zero(complex&lt; float &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:202</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01float_01_4_html_a9c54e89b3f094fb201b007367083c4b5"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01float_01_4.html#a9c54e89b3f094fb201b007367083c4b5">cutlass::TypeTraits&lt; float &gt;::host_type</a></div><div class="ttdeci">float host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:139</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int8__t_01_4_html_a8ed63daf2822140e6f4820721d353112"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a8ed63daf2822140e6f4820721d353112">cutlass::TypeTraits&lt; int8_t &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:59</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4_html_a226f54f55c3a2154e89632ca81a99789"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a226f54f55c3a2154e89632ca81a99789">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::device_type</a></div><div class="ttdeci">complex&lt; float &gt; device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:198</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4_html_a4e6d27a12f835434a16b0f7bc92ae0d2"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a4e6d27a12f835434a16b0f7bc92ae0d2">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::host_type</a></div><div class="ttdeci">complex&lt; float &gt; host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:197</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4_html_a35bedaea951cee4a763a5ddb6a1a2995"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01float_01_4_01_4.html#a35bedaea951cee4a763a5ddb6a1a2995">cutlass::TypeTraits&lt; complex&lt; float &gt; &gt;::unsigned_type</a></div><div class="ttdeci">uint64_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:200</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4_html_ae3ee811cbb4e6f58b2d6a642b92e7e39"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01double_01_4_01_4.html#ae3ee811cbb4e6f58b2d6a642b92e7e39">cutlass::TypeTraits&lt; complex&lt; double &gt; &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(complex&lt; double &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:227</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int_01_4_html_adf3d967f203fd5010c88d02a90ff80f4"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int_01_4.html#adf3d967f203fd5010c88d02a90ff80f4">cutlass::TypeTraits&lt; int &gt;::host_type</a></div><div class="ttdeci">int host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:77</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint8__t_01_4_html_a84de624a6a2a3431cd4f842994a9946d"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a84de624a6a2a3431cd4f842994a9946d">cutlass::TypeTraits&lt; uint8_t &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(host_type x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:71</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4_html_a698ffc92b5a0717c0d3015b034cee4d3"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a698ffc92b5a0717c0d3015b034cee4d3">cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::to_print</a></div><div class="ttdeci">static complex&lt; half_t &gt; to_print(complex&lt; half_t &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:189</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4_html_a5e9a9520dd285a28f29371882e8199b9"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#a5e9a9520dd285a28f29371882e8199b9">cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::unsigned_type</a></div><div class="ttdeci">uint16_t unsigned_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:182</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01half__t_01_4_html_abdd84ca5cd2fe5209602fa730561a85c"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01half__t_01_4.html#abdd84ca5cd2fe5209602fa730561a85c">cutlass::TypeTraits&lt; half_t &gt;::device_type</a></div><div class="ttdeci">half_t device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:126</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int8__t_01_4_html_a2b1e2ad97871de651d54035f97bb1149"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a2b1e2ad97871de651d54035f97bb1149">cutlass::TypeTraits&lt; int8_t &gt;::to_print</a></div><div class="ttdeci">static int to_print(int8_t x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:58</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4_html_ab648e5aaa13ce327cea11b8b18bf2ae5"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#ab648e5aaa13ce327cea11b8b18bf2ae5">cutlass::TypeTraits&lt; complex&lt; half &gt; &gt;::to_device</a></div><div class="ttdeci">static device_type to_device(complex&lt; half &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:173</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4_html_a01f6604dff64aedba173c1ceae9fd773"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half_01_4_01_4.html#a01f6604dff64aedba173c1ceae9fd773">cutlass::TypeTraits&lt; complex&lt; half &gt; &gt;::device_type</a></div><div class="ttdeci">complex&lt; half &gt; device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:170</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01uint8__t_01_4_html_a582965751efd761e874611a3282dbe34"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01uint8__t_01_4.html#a582965751efd761e874611a3282dbe34">cutlass::TypeTraits&lt; uint8_t &gt;::device_type</a></div><div class="ttdeci">uint8_t device_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:66</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01int8__t_01_4_html_a97f22781b0e3895fb981693e20f915d6"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01int8__t_01_4.html#a97f22781b0e3895fb981693e20f915d6">cutlass::TypeTraits&lt; int8_t &gt;::host_type</a></div><div class="ttdeci">int8_t host_type</div><div class="ttdef"><b>Definition:</b> type_traits.h:53</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4_html_ac3cad0e8f771e936bfa2a64df50982dc"><div class="ttname"><a href="structcutlass_1_1TypeTraits_3_01complex_3_01half__t_01_4_01_4.html#ac3cad0e8f771e936bfa2a64df50982dc">cutlass::TypeTraits&lt; complex&lt; half_t &gt; &gt;::remove_negative_zero</a></div><div class="ttdeci">static complex&lt; half_t &gt; remove_negative_zero(complex&lt; half_t &gt; x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:183</div></div>
<div class="ttc" id="structcutlass_1_1half__t_html_aaee5cf278f88e1de09cbebf8cdd2cbe8"><div class="ttname"><a href="structcutlass_1_1half__t.html#aaee5cf278f88e1de09cbebf8cdd2cbe8">cutlass::half_t::raw</a></div><div class="ttdeci">CUTLASS_HOST_DEVICE uint16_t &amp; raw()</div><div class="ttdoc">Accesses raw internal state. </div><div class="ttdef"><b>Definition:</b> half.h:367</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_html_aefa56c124caff14cbee72476bc1c0ab2"><div class="ttname"><a href="structcutlass_1_1TypeTraits.html#aefa56c124caff14cbee72476bc1c0ab2">cutlass::TypeTraits::to_print</a></div><div class="ttdeci">static T to_print(T x)</div><div class="ttdef"><b>Definition:</b> type_traits.h:46</div></div>
<div class="ttc" id="structcutlass_1_1TypeTraits_html"><div class="ttname"><a href="structcutlass_1_1TypeTraits.html">cutlass::TypeTraits</a></div><div class="ttdef"><b>Definition:</b> type_traits.h:42</div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
