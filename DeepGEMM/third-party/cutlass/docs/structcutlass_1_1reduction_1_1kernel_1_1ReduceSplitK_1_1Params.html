<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reduction::kernel::ReduceSplitK&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params Struct Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reduction.html">reduction</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reduction_1_1kernel.html">kernel</a></li><li class="navelem"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">ReduceSplitK</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html">Params</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#pub-methods">Public Member Functions</a> &#124;
<a href="#pub-attribs">Public Attributes</a> &#124;
<a href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reduction::kernel::ReduceSplitK&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params Struct Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html" title="Params structure. ">Params</a> structure.  
</p>

<p><code>#include &lt;<a class="el" href="reduce__split__k_8h_source.html">reduce_split_k.h</a>&gt;</code></p>
<div class="dynheader">
Collaboration diagram for cutlass::reduction::kernel::ReduceSplitK&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params:</div>
<div class="dyncontent">
<div class="center"><img src="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params__coll__graph.png" border="0" usemap="#cutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_3_01Shape___00_01OutputOp___00_01ReductionOp___00_01PartitionsPerStage_01_4_1_1Params_coll__map" alt="Collaboration graph"/></div>
<map name="cutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_3_01Shape___00_01OutputOp___00_01ReductionOp___00_01PartitionsPerStage_01_4_1_1Params_coll__map" id="cutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_3_01Shape___00_01OutputOp___00_01ReductionOp___00_01PartitionsPerStage_01_4_1_1Params_coll__map">
</map>
<center><span class="legend">[<a href="graph_legend.html">legend</a>]</span></center></div>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-methods"></a>
Public Member Functions</h2></td></tr>
<tr class="memitem:a7613c14f567f1179108896db24f61901"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#a7613c14f567f1179108896db24f61901">Params</a> ()</td></tr>
<tr class="separator:a7613c14f567f1179108896db24f61901"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0e145f20b18a1225107762be663ee42"><td class="memItemLeft" align="right" valign="top"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#ae0e145f20b18a1225107762be663ee42">Params</a> (<a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> problem_size_, int partitions_, size_t partition_stride_, <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a006674043f4361cf8e5d63ae903bf9fa">WorkspaceTensorRef</a> workspace_, <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a> destination_, <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a> source_, typename OutputOp::Params output_=typename OutputOp::Params(), typename ReductionOp::Params reduction_=typename ReductionOp::Params())</td></tr>
<tr class="separator:ae0e145f20b18a1225107762be663ee42"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-attribs"></a>
Public Attributes</h2></td></tr>
<tr class="memitem:a4e71c9fe8dab59b795bd7a4a2d33cf0c"><td class="memItemLeft" align="right" valign="top"><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#a4e71c9fe8dab59b795bd7a4a2d33cf0c">problem_size</a></td></tr>
<tr class="separator:a4e71c9fe8dab59b795bd7a4a2d33cf0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a355a2740ee735de6616705c523b68fdd"><td class="memItemLeft" align="right" valign="top">int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#a355a2740ee735de6616705c523b68fdd">partitions</a></td></tr>
<tr class="separator:a355a2740ee735de6616705c523b68fdd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a10fb9f2ac4dc43b02aeb0714ab4ba889"><td class="memItemLeft" align="right" valign="top">size_t&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#a10fb9f2ac4dc43b02aeb0714ab4ba889">partition_stride</a></td></tr>
<tr class="separator:a10fb9f2ac4dc43b02aeb0714ab4ba889"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a8a306dbb3f813297bcf9cebda1067e80"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a006674043f4361cf8e5d63ae903bf9fa">WorkspaceTensorRef</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#a8a306dbb3f813297bcf9cebda1067e80">workspace</a></td></tr>
<tr class="separator:a8a306dbb3f813297bcf9cebda1067e80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a08089218798599f5f47184f8c94723cb"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#a08089218798599f5f47184f8c94723cb">destination</a></td></tr>
<tr class="separator:a08089218798599f5f47184f8c94723cb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:aaf43809bae5b18b2a37e2fa3a934ec15"><td class="memItemLeft" align="right" valign="top"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#aaf43809bae5b18b2a37e2fa3a934ec15">source</a></td></tr>
<tr class="separator:aaf43809bae5b18b2a37e2fa3a934ec15"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac4dbbbd4c98f1be716d5d1d739953e17"><td class="memItemLeft" align="right" valign="top">OutputOp::Params&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#ac4dbbbd4c98f1be716d5d1d739953e17">output</a></td></tr>
<tr class="separator:ac4dbbbd4c98f1be716d5d1d739953e17"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab59614242d435c963b9607eb7da6f5b5"><td class="memItemLeft" align="right" valign="top">ReductionOp::Params&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK_1_1Params.html#ab59614242d435c963b9607eb7da6f5b5">reduction</a></td></tr>
<tr class="separator:ab59614242d435c963b9607eb7da6f5b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Constructor &amp; Destructor Documentation</h2>
<a class="anchor" id="a7613c14f567f1179108896db24f61901"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::Params </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae0e145f20b18a1225107762be663ee42"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::Params </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a>&#160;</td>
          <td class="paramname"><em>problem_size_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>partitions_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">size_t&#160;</td>
          <td class="paramname"><em>partition_stride_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a006674043f4361cf8e5d63ae903bf9fa">WorkspaceTensorRef</a>&#160;</td>
          <td class="paramname"><em>workspace_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a>&#160;</td>
          <td class="paramname"><em>destination_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a>&#160;</td>
          <td class="paramname"><em>source_</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename OutputOp::Params&#160;</td>
          <td class="paramname"><em>output_</em> = <code>typename&#160;OutputOp::Params()</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename ReductionOp::Params&#160;</td>
          <td class="paramname"><em>reduction_</em> = <code>typename&#160;ReductionOp::Params()</code>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a08089218798599f5f47184f8c94723cb"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a> <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::destination</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac4dbbbd4c98f1be716d5d1d739953e17"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">OutputOp::Params <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::output</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a10fb9f2ac4dc43b02aeb0714ab4ba889"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">size_t <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::partition_stride</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a355a2740ee735de6616705c523b68fdd"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">int <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::partitions</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a4e71c9fe8dab59b795bd7a4a2d33cf0c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structcutlass_1_1MatrixCoord.html">MatrixCoord</a> <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::problem_size</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab59614242d435c963b9607eb7da6f5b5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">ReductionOp::Params <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::reduction</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aaf43809bae5b18b2a37e2fa3a934ec15"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#ad3809cf511423cdd0deea5401bee3f35">OutputTensorRef</a> <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::source</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a8a306dbb3f813297bcf9cebda1067e80"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , typename OutputOp_ , typename ReductionOp_ , int PartitionsPerStage = 4&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html#a006674043f4361cf8e5d63ae903bf9fa">WorkspaceTensorRef</a> <a class="el" href="classcutlass_1_1reduction_1_1kernel_1_1ReduceSplitK.html">cutlass::reduction::kernel::ReduceSplitK</a>&lt; Shape_, OutputOp_, ReductionOp_, PartitionsPerStage &gt;::Params::workspace</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="reduce__split__k_8h_source.html">reduce_split_k.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
