<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::transform::PitchLinearWarpRakedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1transform.html">transform</a></li><li class="navelem"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">PitchLinearWarpRakedThreadMap</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-methods">Static Public Member Functions</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::transform::PitchLinearWarpRakedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html">Detail</a></td></tr>
<tr class="memdesc:"><td class="mdescLeft">&#160;</td><td class="mdescRight">Internal details made public to facilitate introspection Iterations along each dimension (concept: PitchLinearShape)  <a href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#details">More...</a><br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:aa1bdccd5962a32c23d5dbd7d3b06de4a"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#aa1bdccd5962a32c23d5dbd7d3b06de4a">TensorCoord</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td></tr>
<tr class="memdesc:aa1bdccd5962a32c23d5dbd7d3b06de4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tensor coordinate.  <a href="#aa1bdccd5962a32c23d5dbd7d3b06de4a">More...</a><br /></td></tr>
<tr class="separator:aa1bdccd5962a32c23d5dbd7d3b06de4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae17e1878ff7cb3ac482c56f838ad1be9"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ae17e1878ff7cb3ac482c56f838ad1be9">Shape</a> = Shape_</td></tr>
<tr class="memdesc:ae17e1878ff7cb3ac482c56f838ad1be9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Tile shape.  <a href="#ae17e1878ff7cb3ac482c56f838ad1be9">More...</a><br /></td></tr>
<tr class="separator:ae17e1878ff7cb3ac482c56f838ad1be9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adcadd14b853c3064ffcb9f00c5df274b"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#adcadd14b853c3064ffcb9f00c5df274b">ThreadAccessShape</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750">kElementsPerAccess</a>, 1 &gt;</td></tr>
<tr class="memdesc:adcadd14b853c3064ffcb9f00c5df274b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Shape of access by each thread.  <a href="#adcadd14b853c3064ffcb9f00c5df274b">More...</a><br /></td></tr>
<tr class="separator:adcadd14b853c3064ffcb9f00c5df274b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac6f4333d33283b867d120c6c43db9036"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ac6f4333d33283b867d120c6c43db9036">Iterations</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::WarpAccessIterations::kContiguous</a>/<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a05bcdb4e8fb32bfe30ac862ffc5de279">Detail::kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::WarpAccessIterations::kStrided</a>/<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a74a1c31d82466657f6d2eb4126ed212f">Detail::kWarpsStrided</a> &gt;</td></tr>
<tr class="separator:ac6f4333d33283b867d120c6c43db9036"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a2b34c242897c93f99d1bf402ab53cf11"><td class="memItemLeft" align="right" valign="top">using&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a2b34c242897c93f99d1bf402ab53cf11">Delta</a> = <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Detail::WarpThreadArrangement::kContiguous *<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750">kElementsPerAccess</a>, Detail::WarpThreadArrangement::kStrided &gt;</td></tr>
<tr class="memdesc:a2b34c242897c93f99d1bf402ab53cf11"><td class="mdescLeft">&#160;</td><td class="mdescRight">Delta betweeen accesses (units of elements, concept: PitchLinearShape)  <a href="#a2b34c242897c93f99d1bf402ab53cf11">More...</a><br /></td></tr>
<tr class="separator:a2b34c242897c93f99d1bf402ab53cf11"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-methods"></a>
Static Public Member Functions</h2></td></tr>
<tr class="memitem:a495158bd07a83fa73c78bc4e41b92c86"><td class="memItemLeft" align="right" valign="top">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#aa1bdccd5962a32c23d5dbd7d3b06de4a">TensorCoord</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a495158bd07a83fa73c78bc4e41b92c86">initial_offset</a> (int thread_id)</td></tr>
<tr class="memdesc:a495158bd07a83fa73c78bc4e41b92c86"><td class="mdescLeft">&#160;</td><td class="mdescRight">Maps thread ID to a coordinate offset within the tensor's logical coordinate space.  <a href="#a495158bd07a83fa73c78bc4e41b92c86">More...</a><br /></td></tr>
<tr class="separator:a495158bd07a83fa73c78bc4e41b92c86"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:ab0d2e70502e80e8f85ef7b83cade093c"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ab0d2e70502e80e8f85ef7b83cade093c">kThreads</a> = Threads</td></tr>
<tr class="memdesc:ab0d2e70502e80e8f85ef7b83cade093c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Number of threads total.  <a href="#ab0d2e70502e80e8f85ef7b83cade093c">More...</a><br /></td></tr>
<tr class="separator:ab0d2e70502e80e8f85ef7b83cade093c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a5d58eb41348e3acb88f815c706b3e750"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750">kElementsPerAccess</a> = ElementsPerAccess</td></tr>
<tr class="memdesc:a5d58eb41348e3acb88f815c706b3e750"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract vector length from Layout.  <a href="#a5d58eb41348e3acb88f815c706b3e750">More...</a><br /></td></tr>
<tr class="separator:a5d58eb41348e3acb88f815c706b3e750"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><h3>template&lt;typename Shape_, int Threads, typename WarpThreadArrangement_, int ElementsPerAccess = 1&gt;<br />
struct cutlass::transform::PitchLinearWarpRakedThreadMap&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;</h3>

<p>Policy defining a warp-raked arrangement in which a shape is partitioned into contiguous elements. </p>
</div><h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="a2b34c242897c93f99d1bf402ab53cf11"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a2b34c242897c93f99d1bf402ab53cf11">Delta</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; Detail::WarpThreadArrangement::kContiguous * <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750">kElementsPerAccess</a>, Detail::WarpThreadArrangement::kStrided &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac6f4333d33283b867d120c6c43db9036"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ac6f4333d33283b867d120c6c43db9036">Iterations</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt; <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#a0d1cfb72e7511d162d123fcb36d181b7">Detail::WarpAccessIterations::kContiguous</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a05bcdb4e8fb32bfe30ac862ffc5de279">Detail::kWarpsContiguous</a>, <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html#aaec0afa0c26627d951d2d2b98a3e5601">Detail::WarpAccessIterations::kStrided</a> / <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap_1_1Detail.html#a74a1c31d82466657f6d2eb4126ed212f">Detail::kWarpsStrided</a> &gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae17e1878ff7cb3ac482c56f838ad1be9"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#ae17e1878ff7cb3ac482c56f838ad1be9">Shape</a> =  Shape_</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="aa1bdccd5962a32c23d5dbd7d3b06de4a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#aa1bdccd5962a32c23d5dbd7d3b06de4a">TensorCoord</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearCoord.html">layout::PitchLinearCoord</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adcadd14b853c3064ffcb9f00c5df274b"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">using <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#adcadd14b853c3064ffcb9f00c5df274b">ThreadAccessShape</a> =  <a class="el" href="structcutlass_1_1layout_1_1PitchLinearShape.html">layout::PitchLinearShape</a>&lt;<a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#a5d58eb41348e3acb88f815c706b3e750">kElementsPerAccess</a>, 1&gt;</td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Function Documentation</h2>
<a class="anchor" id="a495158bd07a83fa73c78bc4e41b92c86"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">static <a class="el" href="cutlass_8h.html#a28c2443a142676d3d71effdae1a986b1">CUTLASS_HOST_DEVICE</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html#aa1bdccd5962a32c23d5dbd7d3b06de4a">TensorCoord</a> <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::initial_offset </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>thread_id</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">inline</span><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="a5d58eb41348e3acb88f815c706b3e750"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::kElementsPerAccess = ElementsPerAccess</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab0d2e70502e80e8f85ef7b83cade093c"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename Shape_ , int Threads, typename WarpThreadArrangement_ , int ElementsPerAccess = 1&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1transform_1_1PitchLinearWarpRakedThreadMap.html">cutlass::transform::PitchLinearWarpRakedThreadMap</a>&lt; Shape_, Threads, WarpThreadArrangement_, ElementsPerAccess &gt;::kThreads = Threads</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="pitch__linear__thread__map_8h_source.html">pitch_linear_thread_map.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
