<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.11"/>
<title>CUTLASS: cutlass::reduction::BatchedReductionTraits&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt; Struct Template Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX","output/HTML-CSS"],
});
</script><script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="cutlass-logo-small.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">CUTLASS
   </div>
   <div id="projectbrief">CUDA Templates for Linear Algebra Subroutines and Solvers</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.11 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="namespaces.html"><span>Namespaces</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="annotated.html"><span>Class&#160;List</span></a></li>
      <li><a href="classes.html"><span>Class&#160;Index</span></a></li>
      <li><a href="inherits.html"><span>Class&#160;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&#160;Members</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="namespacecutlass.html">cutlass</a></li><li class="navelem"><a class="el" href="namespacecutlass_1_1reduction.html">reduction</a></li><li class="navelem"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">BatchedReductionTraits</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#nested-classes">Classes</a> &#124;
<a href="#pub-types">Public Types</a> &#124;
<a href="#pub-static-attribs">Static Public Attributes</a> &#124;
<a href="structcutlass_1_1reduction_1_1BatchedReductionTraits-members.html">List of all members</a>  </div>
  <div class="headertitle">
<div class="title">cutlass::reduction::BatchedReductionTraits&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt; Struct Template Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><code>#include &lt;<a class="el" href="batched__reduction__traits_8h_source.html">batched_reduction_traits.h</a>&gt;</code></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="nested-classes"></a>
Classes</h2></td></tr>
<tr class="memitem:"><td class="memItemLeft" align="right" valign="top">struct &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits_1_1Params.html">Params</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-types"></a>
Public Types</h2></td></tr>
<tr class="memitem:acfeed4cb4e5eec9d8e1ae2b787cc88e2"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#acfeed4cb4e5eec9d8e1ae2b787cc88e2">This_</a></td></tr>
<tr class="separator:acfeed4cb4e5eec9d8e1ae2b787cc88e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a085c72d54426f5eb60f5bffa9c383229"><td class="memItemLeft" align="right" valign="top">typedef <a class="el" href="structcutlass_1_1reduction_1_1BatchedReduction.html">cutlass::reduction::BatchedReduction</a>&lt; <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#acfeed4cb4e5eec9d8e1ae2b787cc88e2">This_</a> &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a085c72d54426f5eb60f5bffa9c383229">KernelClass</a></td></tr>
<tr class="memdesc:a085c72d54426f5eb60f5bffa9c383229"><td class="mdescLeft">&#160;</td><td class="mdescRight">The struct that consumes this Traits.  <a href="#a085c72d54426f5eb60f5bffa9c383229">More...</a><br /></td></tr>
<tr class="separator:a085c72d54426f5eb60f5bffa9c383229"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a3b0e98519b7e551ef6adac69e7406d8f"><td class="memItemLeft" align="right" valign="top">typedef OutputTile_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a3b0e98519b7e551ef6adac69e7406d8f">OutputTile</a></td></tr>
<tr class="separator:a3b0e98519b7e551ef6adac69e7406d8f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ad9d4dcb20a69869ebda639747a16e647"><td class="memItemLeft" align="right" valign="top">typedef SubTile_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ad9d4dcb20a69869ebda639747a16e647">SubTile</a></td></tr>
<tr class="separator:ad9d4dcb20a69869ebda639747a16e647"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a29e8adec50bc7e5c0a91f975dff4a7f3"><td class="memItemLeft" align="right" valign="top">typedef ThreadShape_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a29e8adec50bc7e5c0a91f975dff4a7f3">ThreadShape</a></td></tr>
<tr class="separator:a29e8adec50bc7e5c0a91f975dff4a7f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a1c6cd0a76b2b2fc9cd021b016f30f459"><td class="memItemLeft" align="right" valign="top">typedef ScalarA_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">ScalarA</a></td></tr>
<tr class="memdesc:a1c6cd0a76b2b2fc9cd021b016f30f459"><td class="mdescLeft">&#160;</td><td class="mdescRight">The input pointer type.  <a href="#a1c6cd0a76b2b2fc9cd021b016f30f459">More...</a><br /></td></tr>
<tr class="separator:a1c6cd0a76b2b2fc9cd021b016f30f459"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:adb39cf54a839bdb2e38fbc8a0bf304a8"><td class="memItemLeft" align="right" valign="top">typedef ScalarC_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">ScalarC</a></td></tr>
<tr class="separator:adb39cf54a839bdb2e38fbc8a0bf304a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:abb54e3addfee4097b37deb5cb30fb582"><td class="memItemLeft" align="right" valign="top">typedef ScalarD_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">ScalarD</a></td></tr>
<tr class="memdesc:abb54e3addfee4097b37deb5cb30fb582"><td class="mdescLeft">&#160;</td><td class="mdescRight">The output pointer type.  <a href="#abb54e3addfee4097b37deb5cb30fb582">More...</a><br /></td></tr>
<tr class="separator:abb54e3addfee4097b37deb5cb30fb582"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab89c35cfce0017a47341a1e3b2894e0f"><td class="memItemLeft" align="right" valign="top">typedef ScalarAlphaBeta_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a></td></tr>
<tr class="memdesc:ab89c35cfce0017a47341a1e3b2894e0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The alpha beta type.  <a href="#ab89c35cfce0017a47341a1e3b2894e0f">More...</a><br /></td></tr>
<tr class="separator:ab89c35cfce0017a47341a1e3b2894e0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae7e468b1d372b4b807e2e1089af885ec"><td class="memItemLeft" align="right" valign="top">typedef ScalarAccum_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae7e468b1d372b4b807e2e1089af885ec">ScalarAccum</a></td></tr>
<tr class="memdesc:ae7e468b1d372b4b807e2e1089af885ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">The type for accumulation.  <a href="#ae7e468b1d372b4b807e2e1089af885ec">More...</a><br /></td></tr>
<tr class="separator:ae7e468b1d372b4b807e2e1089af885ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab0c9e548f3ee62746e727127e387a8f4"><td class="memItemLeft" align="right" valign="top">typedef Index_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a></td></tr>
<tr class="memdesc:ab0c9e548f3ee62746e727127e387a8f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">The index.  <a href="#ab0c9e548f3ee62746e727127e387a8f4">More...</a><br /></td></tr>
<tr class="separator:ab0c9e548f3ee62746e727127e387a8f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ae0c016bcbe687063774d8abd554939b6"><td class="memItemLeft" align="right" valign="top">typedef BlockSwizzle_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae0c016bcbe687063774d8abd554939b6">BlockSwizzle</a></td></tr>
<tr class="memdesc:ae0c016bcbe687063774d8abd554939b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">The thread block swizzle.  <a href="#ae0c016bcbe687063774d8abd554939b6">More...</a><br /></td></tr>
<tr class="separator:ae0c016bcbe687063774d8abd554939b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af62fd99fae22a4e4d93cfa0560f1dcc5"><td class="memItemLeft" align="right" valign="top">typedef Functor_&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af62fd99fae22a4e4d93cfa0560f1dcc5">Functor</a></td></tr>
<tr class="separator:af62fd99fae22a4e4d93cfa0560f1dcc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="pub-static-attribs"></a>
Static Public Attributes</h2></td></tr>
<tr class="memitem:a00c71c9a18aaad84f4a48023dbbb454e"><td class="memItemLeft" align="right" valign="top">static const int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a00c71c9a18aaad84f4a48023dbbb454e">ReductionSize</a> = ReductionSize_</td></tr>
<tr class="separator:a00c71c9a18aaad84f4a48023dbbb454e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab4f5f457dbfa6bd250a4c34e1d573a85"><td class="memItemLeft" align="right" valign="top">static const bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab4f5f457dbfa6bd250a4c34e1d573a85">ThreadShapeMultiple2</a> = (ThreadShape::kW % 2 == 0)</td></tr>
<tr class="memdesc:ab4f5f457dbfa6bd250a4c34e1d573a85"><td class="mdescLeft">&#160;</td><td class="mdescRight">check if threadShape is multiple of 2.  <a href="#ab4f5f457dbfa6bd250a4c34e1d573a85">More...</a><br /></td></tr>
<tr class="separator:ab4f5f457dbfa6bd250a4c34e1d573a85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ab35edeae5cd8767bd376fad5f6680e25"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab35edeae5cd8767bd376fad5f6680e25">kThreads</a> = SubTile::kW / ThreadShape::kW</td></tr>
<tr class="separator:ab35edeae5cd8767bd376fad5f6680e25"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:af11a3284195a24e580d2f379f179f05a"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af11a3284195a24e580d2f379f179f05a">maxInReg</a> = maxInReg_</td></tr>
<tr class="separator:af11a3284195a24e580d2f379f179f05a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ac28e31791c5888bbe7b04abe6376a422"><td class="memItemLeft" align="right" valign="top">static int const&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ac28e31791c5888bbe7b04abe6376a422">maxOutReg</a> = maxOutReg_</td></tr>
<tr class="separator:ac28e31791c5888bbe7b04abe6376a422"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<h2 class="groupheader">Member Typedef Documentation</h2>
<a class="anchor" id="ae0c016bcbe687063774d8abd554939b6"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef BlockSwizzle_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae0c016bcbe687063774d8abd554939b6">BlockSwizzle</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="af62fd99fae22a4e4d93cfa0560f1dcc5"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef Functor_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#af62fd99fae22a4e4d93cfa0560f1dcc5">Functor</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab0c9e548f3ee62746e727127e387a8f4"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef Index_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab0c9e548f3ee62746e727127e387a8f4">Index</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a085c72d54426f5eb60f5bffa9c383229"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="structcutlass_1_1reduction_1_1BatchedReduction.html">cutlass::reduction::BatchedReduction</a>&lt;<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#acfeed4cb4e5eec9d8e1ae2b787cc88e2">This_</a>&gt; <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a085c72d54426f5eb60f5bffa9c383229">KernelClass</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a3b0e98519b7e551ef6adac69e7406d8f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef OutputTile_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a3b0e98519b7e551ef6adac69e7406d8f">OutputTile</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a1c6cd0a76b2b2fc9cd021b016f30f459"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ScalarA_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a1c6cd0a76b2b2fc9cd021b016f30f459">ScalarA</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ae7e468b1d372b4b807e2e1089af885ec"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ScalarAccum_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ae7e468b1d372b4b807e2e1089af885ec">ScalarAccum</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab89c35cfce0017a47341a1e3b2894e0f"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ScalarAlphaBeta_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ab89c35cfce0017a47341a1e3b2894e0f">ScalarAlphaBeta</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="adb39cf54a839bdb2e38fbc8a0bf304a8"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ScalarC_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#adb39cf54a839bdb2e38fbc8a0bf304a8">ScalarC</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="abb54e3addfee4097b37deb5cb30fb582"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ScalarD_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#abb54e3addfee4097b37deb5cb30fb582">ScalarD</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ad9d4dcb20a69869ebda639747a16e647"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef SubTile_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#ad9d4dcb20a69869ebda639747a16e647">SubTile</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="acfeed4cb4e5eec9d8e1ae2b787cc88e2"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">BatchedReductionTraits</a>&lt;ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_&gt; <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#acfeed4cb4e5eec9d8e1ae2b787cc88e2">This_</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a29e8adec50bc7e5c0a91f975dff4a7f3"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">typedef ThreadShape_ <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::<a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html#a29e8adec50bc7e5c0a91f975dff4a7f3">ThreadShape</a></td>
        </tr>
      </table>
</div><div class="memdoc">

</div>
</div>
<h2 class="groupheader">Member Data Documentation</h2>
<a class="anchor" id="ab35edeae5cd8767bd376fad5f6680e25"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::kThreads = SubTile::kW / ThreadShape::kW</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">
<p>Parameteres object constructable on the host The number of threads per thread block. can be deduced </p>

</div>
</div>
<a class="anchor" id="af11a3284195a24e580d2f379f179f05a"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::maxInReg = maxInReg_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ac28e31791c5888bbe7b04abe6376a422"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">int const <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::maxOutReg = maxOutReg_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="a00c71c9a18aaad84f4a48023dbbb454e"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const int <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::ReductionSize = ReductionSize_</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<a class="anchor" id="ab4f5f457dbfa6bd250a4c34e1d573a85"></a>
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename ScalarA_ , typename ScalarC_ , typename ScalarD_ , typename ScalarAlphaBeta_ , typename ScalarAccum_ , int ReductionSize_ = 1, typename OutputTile_  = Shape&lt;1, 1, 128&gt;, typename SubTile_  = Shape&lt;1, 1, 64&gt;, typename ThreadShape_  = Shape&lt;1, 1, 2&gt;, typename Index_  = int, typename BlockSwizzle_  = DefaultBlockSwizzle, int maxInReg_ = 160, int maxOutReg_ = 64, typename Functor_  = typename cutlass::gemm::LinearScaling&lt;ScalarAlphaBeta_, typename cutlass::gemm::FragmentMultiplyAdd&lt;ScalarAlphaBeta_, ScalarAccum_, (ThreadShape_::kW % 2 == 0)&gt; &gt;&gt; </div>
<table class="mlabels">
  <tr>
  <td class="mlabels-left">
      <table class="memname">
        <tr>
          <td class="memname">const bool <a class="el" href="structcutlass_1_1reduction_1_1BatchedReductionTraits.html">cutlass::reduction::BatchedReductionTraits</a>&lt; ScalarA_, ScalarC_, ScalarD_, ScalarAlphaBeta_, ScalarAccum_, ReductionSize_, OutputTile_, SubTile_, ThreadShape_, Index_, BlockSwizzle_, maxInReg_, maxOutReg_, Functor_ &gt;::ThreadShapeMultiple2 = (ThreadShape::kW % 2 == 0)</td>
        </tr>
      </table>
  </td>
  <td class="mlabels-right">
<span class="mlabels"><span class="mlabel">static</span></span>  </td>
  </tr>
</table>
</div><div class="memdoc">

</div>
</div>
<hr/>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="batched__reduction__traits_8h_source.html">batched_reduction_traits.h</a></li>
</ul>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.11
</small></address>
</body>
</html>
